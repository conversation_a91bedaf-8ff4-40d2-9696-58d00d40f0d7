# 基于Python路线规划数据分析推荐系统 - 项目结构说明

## 项目概述

本项目是一个基于Python和Django开发的智能路线规划系统，集成了数据分析和个性化推荐功能。系统提供路线规划、历史记录管理、数据分析可视化、个性化推荐等功能。

## 项目结构

```
/
├── logs/                     # 系统日志目录
│   └── debug.log            # 调试日志文件
├── route_planner/           # Django项目配置目录
│   ├── __init__.py          # Python包初始化文件
│   ├── __pycache__/         # Python字节码缓存目录
│   ├── asgi.py              # ASGI配置文件
│   ├── settings.py          # Django项目设置
│   ├── urls.py              # 主URL路由配置
│   └── wsgi.py              # WSGI配置文件
├── routes/                  # 主应用目录
│   ├── __init__.py          # Python包初始化文件
│   ├── __pycache__/         # Python字节码缓存目录
│   ├── management/          # Django管理命令目录
│   │   ├── __init__.py      # Python包初始化文件
│   │   ├── __pycache__/     # Python字节码缓存目录
│   │   └── commands/        # 自定义管理命令目录
│   │       ├── __init__.py  # Python包初始化文件
│   │       └── generate_mock_data.py  # 生成模拟数据命令
│   ├── migrations/          # 数据库迁移文件目录
│   │   ├── __init__.py      # Python包初始化文件
│   │   ├── __pycache__/     # Python字节码缓存目录
│   │   └── 0001_initial.py  # 初始数据库迁移文件
│   ├── recommenders/        # 推荐系统算法实现目录
│   │   ├── __init__.py      # 推荐系统模块初始化文件
│   │   ├── __pycache__/     # Python字节码缓存目录
│   │   ├── collaborative_filtering.py  # 协同过滤推荐算法
│   │   ├── ensemble.py      # 集成推荐算法
│   │   ├── location_based.py # 基于地理位置的推荐算法
│   │   ├── matrix_factorization.py  # 矩阵分解推荐算法
│   │   └── recommendations.py  # 推荐系统核心功能和日志查看
│   ├── static/              # 静态文件目录
│   │   └── routes/          # 应用静态文件目录
│   │       ├── css/         # CSS样式文件目录
│   │       │   └── style.css  # 主样式文件
│   │       └── js/          # JavaScript文件目录
│   │           ├── amap_echarts.js  # 高德地图与ECharts集成
│   │           ├── analytics_comparison.js  # 分析对比功能
│   │           ├── analytics_overview.js    # 分析概览功能
│   │           ├── analytics_spatial.js     # 空间分析功能
│   │           ├── analytics_temporal.js    # 时间分析功能
│   │           ├── analytics_temporal_spatial_comparison.js  # 时空对比分析
│   │           ├── echarts.min.js     # ECharts图表库
│   │           ├── input_suggestion.js  # 输入建议功能
│   │           ├── main.js            # 主要JavaScript功能
│   │           └── maps.js            # 高德地图API文件
│   ├── templates/           # HTML模板文件目录
│   │   └── routes/          # 应用模板文件目录
│   │       ├── analytics.html     # 数据分析页面
│   │       ├── base.html          # 基础模板
│   │       ├── index.html         # 首页模板
│   │       ├── login.html         # 登录页面
│   │       ├── profile.html       # 个人资料页面
│   │       ├── recommendations.html  # 推荐页面
│   │       ├── register.html      # 注册页面
│   │       ├── search.html        # 搜索页面
│   │       └── tags.html          # 标签管理页面
│   ├── views/               # 视图函数模块化目录
│   │   ├── __init__.py      # 视图模块初始化文件
│   │   ├── __pycache__/     # Python字节码缓存目录
│   │   ├── analytics.py     # 数据分析相关视图
│   │   ├── auth.py          # 用户认证相关视图
│   │   ├── core.py          # 核心功能视图
│   │   ├── profile.py       # 用户资料管理视图
│   │   ├── search.py        # 搜索功能视图
│   │   ├── tags.py          # 标签管理视图
│   │   └── utils.py         # 工具函数
│   ├── admin.py             # Django管理后台配置
│   ├── apps.py              # 应用配置
│   ├── middleware.py        # 自定义中间件
│   ├── models.py            # 数据模型定义
│   ├── session_middleware.py  # 会话管理中间件
│   ├── tests.py             # 测试文件
│   ├── urls.py              # URL路由配置
│   └── views.py             # 基础视图函数
├── venv/                    # Python虚拟环境目录
│   ├── Lib/                 # 虚拟环境库文件
│   ├── Scripts/             # 虚拟环境脚本文件
│   └── pyvenv.cfg           # 虚拟环境配置文件
├── manage.py                # Django项目管理脚本
├── python-3.11.4-amd64.exe # Python安装程序
├── requirements.txt         # 项目依赖文件
├── route_planner.sql        # 数据库备份文件
└── 项目结构说明.md          # 项目结构说明文档（本文件）
```

## 核心模块详细说明

### 1. 数据模型 (routes/models.py)

定义了系统的核心数据结构，包括：

- **UserProfile**: 用户个人资料扩展
  - `user`: 关联到Django用户模型（OneToOneField）
  - `phone`: 手机号（CharField，可选）
  - `default_city`: 默认城市（CharField，可选）
  - `common_addresses`: 常用地址JSON存储（TextField）
  - `created_time`: 创建时间（DateTimeField，自动添加）
  - 方法: `get_common_addresses()`, `add_common_address()`

- **RouteSearch**: 路线搜索记录（主要数据模型）
  - `user`: 关联用户（ForeignKey，可为空支持匿名用户）
  - `origin/destination`: 起点/终点坐标（CharField，最大255字符）
  - `origin_name/destination_name`: 起点/终点名称（CharField，可选）
  - `distance`: 距离，单位米（IntegerField，可选）
  - `duration`: 时间，单位秒（IntegerField，可选）
  - `_route_data`: 路线数据JSON（TextField，内部字段）
  - `created_time`: 创建时间（DateTimeField，自动添加，有索引）
  - `is_favorite`: 是否收藏（BooleanField，默认False）
  - `route_tag`: 路线标签（CharField，最大50字符，默认空字符串）
  - 属性: `route_data` (property，用于JSON数据的获取和设置)

- **UserTag**: 用户标签模型
  - `user`: 关联用户（ForeignKey）
  - `name`: 标签名称（CharField，最大50字符）
  - `created_time`: 创建时间（DateTimeField，自动添加）
  - 约束: 同一用户不能有重复的标签名（unique_together）

### 2. 推荐系统 (routes/recommenders/)

包含多种推荐算法的实现：

- **__init__.py**: 推荐系统模块初始化文件
  - 导入所有推荐算法，提供统一的接口
  - 包含错误处理和备用函数

- **collaborative_filtering.py**: 基于物品的协同过滤推荐
  - `ItemBasedCF`: 核心类，实现基于物品的协同过滤算法
  - 方法: `_build_route_user_matrix()`, `_compute_route_similarity()`, `recommend()`
  - `get_item_cf_recommendations()`: 对外接口函数
  - 使用余弦相似度计算路线之间的相似性

- **location_based.py**: 基于地理位置的推荐
  - `LocationBasedRecommender`: 核心类，实现基于地理位置的推荐算法
  - 使用DBSCAN算法进行地理位置聚类
  - 方法: `_cluster_destinations()`, `_get_popular_destinations_in_area()`, `recommend()`
  - `get_location_recommendations()`: 对外接口函数

- **matrix_factorization.py**: 基于矩阵分解的推荐
  - `MatrixFactorizationRecommender`: 核心类
  - 实现SVD(奇异值分解)算法
  - 通过用户-路线矩阵分解发现潜在特征
  - `get_mf_recommendations()`: 对外接口函数

- **ensemble.py**: 集成推荐算法
  - `RecommendationEnsemble`: 推荐系统集成器
  - 整合多种推荐算法的结果
  - 提供统一的推荐接口和结果融合
  - `get_recommendations_api()`: 对外API接口

- **recommendations.py**: 推荐系统核心功能
  - `route_recommendations()`: 基于用户历史路线数据的推荐
  - `view_logs()`: 系统日志查看功能（仅管理员）

### 3. 视图模块 (routes/views/)

视图函数按功能模块化组织：

- **__init__.py**: 视图模块初始化文件
  - 导入所有视图函数，使它们可以被外部访问
  - 统一的视图函数导出接口

- **auth.py**: 用户认证相关视图
  - `login_view()`: 用户登录视图
  - `register_view()`: 用户注册视图
  - `logout_view()`: 用户登出视图

- **core.py**: 核心功能视图
  - `index()`: 首页视图，显示路线规划主界面
  - `route_planning()`: 路线规划API
  - `route_history()`: 历史记录API
  - `route_detail()`: 路线详情API
  - `route_history_all()`: 获取所有历史路线数据（用于热力图）
  - `toggle_favorite()`: 切换收藏状态
  - `update_route_tag()`: 更新路线标签
  - `analytics_view()`: 数据分析页面视图
  - `search_view()`: 搜索页面视图
  - `recommendations_view()`: 推荐页面视图

- **profile.py**: 用户资料管理视图
  - `profile_view()`: 个人资料页面视图
  - `add_favorite_address()`: 添加常用地址
  - `delete_favorite_address()`: 删除常用地址

- **search.py**: 搜索功能视图
  - `search_routes()`: 路线搜索API（基础版本）
  - `search_routes_v2()`: 路线搜索API（增强版本，支持分页）

- **tags.py**: 标签管理视图
  - `tags_view()`: 标签管理页面视图
  - `get_user_tags()`: 获取用户标签列表API
  - `save_user_tag()`: 保存用户标签API
  - `delete_user_tag()`: 删除用户标签API
  - `get_tag_stats()`: 获取标签统计数据API
  - `apply_batch_tag()`: 批量应用标签API

- **analytics.py**: 数据分析相关视图（1400+行代码）
  - `analytics_overview()`: 获取概览数据API
  - `analytics_travel_modes()`: 获取出行方式分析数据API
  - `analytics_popular_destinations()`: 获取热门目的地数据API
  - `analytics_daily_trend()`: 获取每日规划次数趋势数据API
  - `analytics_monthly_trend()`: 获取月度规划趋势数据API
  - `analytics_popular_routes()`: 获取热门路线数据API
  - `analytics_mode_comparison()`: 获取出行方式对比数据API
  - `analytics_distance_duration_scatter()`: 获取距离时间散点图数据API
  - `analytics_tag_analysis()`: 获取路线标签分析数据API
  - `analytics_user_behavior()`: 获取用户行为分析数据API
  - `time_space_heatmap()`: 时空热力图数据API
  - `time_modes_comparison()`: 时间出行方式对比API
  - `region_time_density()`: 区域时间密度API
  - `periodic_pattern_analysis()`: 周期性模式分析API

- **utils.py**: 工具函数
  - `get_mode_display()`: 获取出行方式的中文显示名称
  - `get_date_range()`: 获取日期范围查询参数
  - `extract_regions()`: 从地址名称中提取区域信息

### 4. URL配置 (routes/urls.py)

定义了系统的所有API端点和页面路由，包括：

- **基础页面路由**: 首页、登录、注册、个人资料、搜索、推荐
- **路线管理API**: 路线规划、历史记录、收藏、标签管理
- **数据分析API**: 各种分析视图和统计数据端点
- **推荐系统API**: 基础推荐和高级推荐接口
- **标签管理API**: 标签CRUD操作和统计功能
- **用户管理API**: 个人资料和地址管理

### 5. 静态文件 (routes/static/routes/)

- **CSS文件**: 
  - `style.css`: 主样式文件，定义了整个系统的视觉风格

- **JavaScript文件**:
  - `main.js`: 主要功能脚本，处理地图显示和路线规划
  - `maps.js`: 高德地图API文件（本地化）
  - `amap_echarts.js`: 高德地图与ECharts图表库的集成
  - `input_suggestion.js`: 输入建议和自动完成功能
  - `echarts.min.js`: ECharts图表库
  - `analytics_*.js`: 各种数据分析功能的前端脚本

### 6. 模板文件 (routes/templates/routes/)

- **base.html**: 基础模板，定义了页面的基本结构和导航
- **index.html**: 首页模板，路线规划主界面
- **login.html/register.html**: 用户认证页面
- **profile.html**: 个人资料管理页面
- **analytics.html**: 数据分析页面，包含各种图表和统计信息
- **recommendations.html**: 推荐页面，显示个性化路线推荐
- **search.html**: 搜索页面，支持路线搜索和过滤
- **tags.html**: 标签管理页面，支持标签的增删改查和批量操作

## 技术栈

- **后端框架**: Django 3.0.10
- **数据库**: MySQL（配置在settings.py中）
- **前端技术**: HTML5, CSS3, JavaScript, Bootstrap
- **地图服务**: 高德地图API
- **数据可视化**: ECharts
- **数据分析**: NumPy, Scikit-learn
- **推荐算法**: 协同过滤, 矩阵分解, 地理位置聚类
- **开发环境**: Python 3.11.4

## 核心功能

1. **路线规划**: 基于高德地图API的多种出行方式路线规划
2. **历史记录**: 用户路线搜索历史的存储和管理
3. **数据分析**: 多维度的路线数据分析和可视化
4. **个性化推荐**: 基于多种算法的智能路线推荐
5. **标签管理**: 用户自定义标签系统和批量操作
6. **用户管理**: 完整的用户认证和个人资料管理系统

## 数据库配置

- **数据库类型**: MySQL
- **数据库名**: route_planner
- **字符集**: utf8mb4
- **时区**: Asia/Shanghai
- **备份文件**: route_planner.sql

## 日志系统

- **日志目录**: logs/
- **日志文件**: debug.log
- **日志级别**: DEBUG（开发环境）
- **日志查看**: 管理员可通过/system-logs/查看系统日志
