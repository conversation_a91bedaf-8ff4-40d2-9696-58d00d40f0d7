INFO 2025-07-27 20:27:32,034 autoreload 12808 15084 Watching for file changes with StatReloader
INFO 2025-07-27 20:27:32,034 autoreload 12808 15084 Watching for file changes with StatReloader
INFO 2025-07-27 20:27:34,148 basehttp 12808 17364 "GET / HTTP/1.1" 200 97354
INFO 2025-07-27 20:27:34,148 basehttp 12808 17364 "GET / HTTP/1.1" 200 97354
INFO 2025-07-27 20:27:34,194 basehttp 12808 17364 "GET /static/routes/css/style.css HTTP/1.1" 200 5807
INFO 2025-07-27 20:27:34,194 basehttp 12808 17364 "GET /static/routes/css/style.css HTTP/1.1" 200 5807
INFO 2025-07-27 20:27:34,197 basehttp 12808 17364 "GET /static/routes/js/amap_echarts.js HTTP/1.1" 200 54230
INFO 2025-07-27 20:27:34,197 basehttp 12808 17364 "GET /static/routes/js/amap_echarts.js HTTP/1.1" 200 54230
INFO 2025-07-27 20:27:34,200 basehttp 12808 17364 "GET /static/routes/js/main.js HTTP/1.1" 200 88204
INFO 2025-07-27 20:27:34,200 basehttp 12808 17364 "GET /static/routes/js/main.js HTTP/1.1" 200 88204
INFO 2025-07-27 20:27:34,202 basehttp 12808 17364 "GET /static/routes/js/input_suggestion.js HTTP/1.1" 200 12024
INFO 2025-07-27 20:27:34,202 basehttp 12808 17364 "GET /static/routes/js/input_suggestion.js HTTP/1.1" 200 12024
INFO 2025-07-27 20:27:34,205 basehttp 12808 18100 "GET /static/routes/js/maps.js HTTP/1.1" 200 1384453
INFO 2025-07-27 20:27:34,205 basehttp 12808 18100 "GET /static/routes/js/maps.js HTTP/1.1" 200 1384453
WARNING 2025-07-27 20:27:38,691 log 12808 18100 Not Found: /favicon.ico
WARNING 2025-07-27 20:27:38,691 log 12808 18100 Not Found: /favicon.ico
WARNING 2025-07-27 20:27:38,691 basehttp 12808 18100 "GET /favicon.ico HTTP/1.1" 404 11021
WARNING 2025-07-27 20:27:38,691 basehttp 12808 18100 "GET /favicon.ico HTTP/1.1" 404 11021
INFO 2025-07-27 20:27:39,716 basehttp 12808 18100 "GET /api/route-history/ HTTP/1.1" 200 5019
INFO 2025-07-27 20:27:39,716 basehttp 12808 18100 "GET /api/route-history/ HTTP/1.1" 200 5019
INFO 2025-07-27 20:27:41,251 basehttp 12808 18100 "GET /search/ HTTP/1.1" 200 25417
INFO 2025-07-27 20:27:41,251 basehttp 12808 18100 "GET /search/ HTTP/1.1" 200 25417
INFO 2025-07-27 20:27:41,308 basehttp 12808 18100 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2686
INFO 2025-07-27 20:27:41,308 basehttp 12808 18100 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2686
INFO 2025-07-27 20:27:42,042 basehttp 12808 18100 "GET /recommendations/ HTTP/1.1" 200 23859
INFO 2025-07-27 20:27:42,042 basehttp 12808 18100 "GET /recommendations/ HTTP/1.1" 200 23859
INFO 2025-07-27 20:27:43,664 basehttp 12808 18100 "GET /login/ HTTP/1.1" 200 12628
INFO 2025-07-27 20:27:43,664 basehttp 12808 18100 "GET /login/ HTTP/1.1" 200 12628
INFO 2025-07-27 20:27:46,008 basehttp 12808 18100 "POST /login/ HTTP/1.1" 200 81
INFO 2025-07-27 20:27:46,008 basehttp 12808 18100 "POST /login/ HTTP/1.1" 200 81
INFO 2025-07-27 20:27:46,042 basehttp 12808 18100 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:27:46,042 basehttp 12808 18100 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:27:47,164 basehttp 12808 18100 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:27:47,164 basehttp 12808 18100 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:27:48,339 basehttp 12808 18100 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:27:48,339 basehttp 12808 18100 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:27:48,369 recommendations 12808 18100 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:27:48,409 basehttp 12808 18100 "GET /api/route-recommendations/?_=1753619268351 HTTP/1.1" 200 2283
INFO 2025-07-27 20:27:48,409 basehttp 12808 18100 "GET /api/route-recommendations/?_=1753619268351 HTTP/1.1" 200 2283
INFO 2025-07-27 20:27:49,017 ensemble 12808 18100 �յ��Ƽ�API�����û�: 1���㷨: item_cf������: 5
INFO 2025-07-27 20:27:49,019 ensemble 12808 18100 ��ʼ���Ƽ����������û�: 1���Ƽ�����: 5
INFO 2025-07-27 20:27:49,020 ensemble 12808 18100 Ϊ�û� 1 ��ȡ item_cf �㷨���Ƽ�
INFO 2025-07-27 20:27:49,020 collaborative_filtering 12808 18100 ��ʼ��������Ʒ��Эͬ�����Ƽ��㷨��top_n=5, min_similarity=0.001
INFO 2025-07-27 20:27:49,025 collaborative_filtering 12808 18100 �ҵ� 193 ����ͬ��·��
INFO 2025-07-27 20:27:51,214 collaborative_filtering 12808 18100 ������ 193 ��·�ߵ��û�����
INFO 2025-07-27 20:27:51,214 collaborative_filtering 12808 18100 ·��-�û������й��� 650 ������Ԫ��
INFO 2025-07-27 20:27:51,215 collaborative_filtering 12808 18100 �����а��� 11 ���û�
INFO 2025-07-27 20:27:51,228 collaborative_filtering 12808 18100 ����õ� 16938 ������·�ߣ�ƽ��ÿ��·���� 87.76 ������·��
INFO 2025-07-27 20:27:51,230 collaborative_filtering 12808 18100 �û� 1 �� 55 ����ʷ·��
INFO 2025-07-27 20:27:51,231 collaborative_filtering 12808 18100 ��ѡ�Ƽ�·������: 89
INFO 2025-07-27 20:27:51,231 collaborative_filtering 12808 18100 ѡȡ�� 5 ���Ƽ�·��
INFO 2025-07-27 20:27:51,232 ensemble 12808 18100 �ɹ���ȡ�� 5 ���Ƽ�
INFO 2025-07-27 20:27:51,232 ensemble 12808 18100 �㷨 item_cf ԭʼ�Ƽ�����: 5
INFO 2025-07-27 20:27:51,232 ensemble 12808 18100 ȥ�غ�ʣ�� 5 ���Ƽ�
INFO 2025-07-27 20:27:51,233 basehttp 12808 18100 "GET /api/advanced-recommendations/?algorithm=item_cf&_=1753619269009 HTTP/1.1" 200 2565
INFO 2025-07-27 20:27:51,233 basehttp 12808 18100 "GET /api/advanced-recommendations/?algorithm=item_cf&_=1753619269009 HTTP/1.1" 200 2565
INFO 2025-07-27 20:27:51,866 ensemble 12808 18100 �յ��Ƽ�API�����û�: 1���㷨: matrix_factorization������: 5
INFO 2025-07-27 20:27:51,866 ensemble 12808 18100 ��ʼ���Ƽ����������û�: 1���Ƽ�����: 5
INFO 2025-07-27 20:27:51,867 ensemble 12808 18100 Ϊ�û� 1 ��ȡ matrix_factorization �㷨���Ƽ�
INFO 2025-07-27 20:27:51,892 ensemble 12808 18100 �ɹ���ȡ�� 5 ���Ƽ�
INFO 2025-07-27 20:27:51,892 ensemble 12808 18100 �㷨 matrix_factorization ԭʼ�Ƽ�����: 5
INFO 2025-07-27 20:27:51,892 ensemble 12808 18100 ȥ�غ�ʣ�� 5 ���Ƽ�
INFO 2025-07-27 20:27:51,893 basehttp 12808 18100 "GET /api/advanced-recommendations/?algorithm=matrix_factorization&_=1753619271857 HTTP/1.1" 200 2447
INFO 2025-07-27 20:27:51,893 basehttp 12808 18100 "GET /api/advanced-recommendations/?algorithm=matrix_factorization&_=1753619271857 HTTP/1.1" 200 2447
INFO 2025-07-27 20:27:53,878 basehttp 12808 18100 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-27 20:27:53,878 basehttp 12808 18100 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-07-27 20:27:53,894 basehttp 12808 18100 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4387
INFO 2025-07-27 20:27:53,894 basehttp 12808 18100 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4387
INFO 2025-07-27 20:27:53,919 basehttp 12808 17700 "GET /static/admin/simpleui-x/css/login.css?_=2.1 HTTP/1.1" 200 500
INFO 2025-07-27 20:27:53,919 basehttp 12808 17700 "GET /static/admin/simpleui-x/css/login.css?_=2.1 HTTP/1.1" 200 500
INFO 2025-07-27 20:27:53,921 basehttp 12808 11792 "GET /static/admin/simpleui-x/js/vue.min.js?_=2025.06.24 HTTP/1.1" 200 93675
INFO 2025-07-27 20:27:53,921 basehttp 12808 11792 "GET /static/admin/simpleui-x/js/vue.min.js?_=2025.06.24 HTTP/1.1" 200 93675
INFO 2025-07-27 20:27:53,921 basehttp 12808 17364 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/css/all.min.css HTTP/1.1" 200 101784
INFO 2025-07-27 20:27:53,921 basehttp 12808 17364 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/css/all.min.css HTTP/1.1" 200 101784
INFO 2025-07-27 20:27:53,923 basehttp 12808 13164 "GET /static/admin/simpleui-x/img/logo.png HTTP/1.1" 200 115997
INFO 2025-07-27 20:27:53,923 basehttp 12808 13164 "GET /static/admin/simpleui-x/img/logo.png HTTP/1.1" 200 115997
INFO 2025-07-27 20:27:53,925 basehttp 12808 17700 "GET /static/admin/simpleui-x/js/login.js?_=3.3 HTTP/1.1" 200 669
INFO 2025-07-27 20:27:53,925 basehttp 12808 17700 "GET /static/admin/simpleui-x/js/login.js?_=3.3 HTTP/1.1" 200 669
INFO 2025-07-27 20:27:53,926 basehttp 12808 11792 "GET /static/admin/simpleui-x/elementui/umd/locale/en.js?_=2025.06.24 HTTP/1.1" 200 3516
INFO 2025-07-27 20:27:53,926 basehttp 12808 11792 "GET /static/admin/simpleui-x/elementui/umd/locale/en.js?_=2025.06.24 HTTP/1.1" 200 3516
INFO 2025-07-27 20:27:53,927 basehttp 12808 18100 "GET /static/admin/simpleui-x/elementui/theme-chalk/index.css HTTP/1.1" 200 240033
INFO 2025-07-27 20:27:53,927 basehttp 12808 18100 "GET /static/admin/simpleui-x/elementui/theme-chalk/index.css HTTP/1.1" 200 240033
INFO 2025-07-27 20:27:53,928 basehttp 12808 13164 "GET /static/admin/simpleui-x/particles/particles.js HTTP/1.1" 200 51264
INFO 2025-07-27 20:27:53,928 basehttp 12808 13164 "GET /static/admin/simpleui-x/particles/particles.js HTTP/1.1" 200 51264
INFO 2025-07-27 20:27:53,929 basehttp 12808 17700 "GET /static/admin/simpleui-x/particles/app.js HTTP/1.1" 200 3728
INFO 2025-07-27 20:27:53,929 basehttp 12808 17700 "GET /static/admin/simpleui-x/particles/app.js HTTP/1.1" 200 3728
INFO 2025-07-27 20:27:53,931 basehttp 12808 17364 "GET /static/admin/simpleui-x/elementui/index.js?_=2025.06.24 HTTP/1.1" 200 664912
INFO 2025-07-27 20:27:53,931 basehttp 12808 17364 "GET /static/admin/simpleui-x/elementui/index.js?_=2025.06.24 HTTP/1.1" 200 664912
INFO 2025-07-27 20:27:53,935 basehttp 12808 17364 "GET /static/admin/simpleui-x/img/bg.svg HTTP/1.1" 200 9547
INFO 2025-07-27 20:27:53,935 basehttp 12808 17364 "GET /static/admin/simpleui-x/img/bg.svg HTTP/1.1" 200 9547
INFO 2025-07-27 20:27:53,987 basehttp 12808 17364 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 150472
INFO 2025-07-27 20:27:53,987 basehttp 12808 17364 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-solid-900.woff2 HTTP/1.1" 200 150472
INFO 2025-07-27 20:27:55,359 basehttp 12808 17364 "GET /static/admin/simpleui-x/elementui/theme-chalk/fonts/element-icons.woff HTTP/1.1" 200 28200
INFO 2025-07-27 20:27:55,359 basehttp 12808 17364 "GET /static/admin/simpleui-x/elementui/theme-chalk/fonts/element-icons.woff HTTP/1.1" 200 28200
INFO 2025-07-27 20:27:56,007 basehttp 12808 17364 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4630
INFO 2025-07-27 20:27:56,007 basehttp 12808 17364 "POST /admin/login/?next=/admin/ HTTP/1.1" 200 4630
INFO 2025-07-27 20:28:13,386 autoreload 18404 14592 Watching for file changes with StatReloader
INFO 2025-07-27 20:28:13,386 autoreload 18404 14592 Watching for file changes with StatReloader
INFO 2025-07-27 20:28:18,986 basehttp 18404 16132 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:28:18,986 basehttp 18404 16132 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:28:20,223 basehttp 18404 16132 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:28:20,223 basehttp 18404 16132 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:28:21,311 basehttp 18404 16132 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-27 20:28:21,311 basehttp 18404 16132 "POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
INFO 2025-07-27 20:28:21,339 basehttp 18404 16132 "GET /admin/ HTTP/1.1" 200 19403
INFO 2025-07-27 20:28:21,339 basehttp 18404 16132 "GET /admin/ HTTP/1.1" 200 19403
INFO 2025-07-27 20:28:21,392 basehttp 18404 18968 "GET /static/admin/simpleui-x/waves/waves.min.css?_=2.1 HTTP/1.1" 200 3861
INFO 2025-07-27 20:28:21,392 basehttp 18404 18968 "GET /static/admin/simpleui-x/waves/waves.min.css?_=2.1 HTTP/1.1" 200 3861
INFO 2025-07-27 20:28:21,394 basehttp 18404 18968 "GET /static/admin/simpleui-x/automatic/segment.js?_=2025.06.24 HTTP/1.1" 200 1194
INFO 2025-07-27 20:28:21,394 basehttp 18404 18968 "GET /static/admin/simpleui-x/automatic/segment.js?_=2025.06.24 HTTP/1.1" 200 1194
INFO 2025-07-27 20:28:21,395 basehttp 18404 16132 "GET /static/admin/simpleui-x/css/index.css?_=3.2 HTTP/1.1" 200 6994
INFO 2025-07-27 20:28:21,395 basehttp 18404 16132 "GET /static/admin/simpleui-x/css/index.css?_=3.2 HTTP/1.1" 200 6994
INFO 2025-07-27 20:28:21,396 basehttp 18404 18968 "GET /static/admin/simpleui-x/locale/en-us.js?_=2025.06.24 HTTP/1.1" 200 811
INFO 2025-07-27 20:28:21,396 basehttp 18404 18968 "GET /static/admin/simpleui-x/locale/en-us.js?_=2025.06.24 HTTP/1.1" 200 811
INFO 2025-07-27 20:28:21,396 basehttp 18404 19536 "GET /static/admin/simpleui-x/automatic/dicts.js?_=2025.06.24 HTTP/1.1" 200 12677
INFO 2025-07-27 20:28:21,396 basehttp 18404 19536 "GET /static/admin/simpleui-x/automatic/dicts.js?_=2025.06.24 HTTP/1.1" 200 12677
INFO 2025-07-27 20:28:21,397 basehttp 18404 16132 "GET /static/admin/simpleui-x/locale/zh-hans.js?_=2025.06.24 HTTP/1.1" 200 1254
INFO 2025-07-27 20:28:21,397 basehttp 18404 16132 "GET /static/admin/simpleui-x/locale/zh-hans.js?_=2025.06.24 HTTP/1.1" 200 1254
INFO 2025-07-27 20:28:21,398 basehttp 18404 18968 "GET /static/admin/simpleui-x/js/cookie.js?_=2025.06.24 HTTP/1.1" 200 524
INFO 2025-07-27 20:28:21,398 basehttp 18404 18968 "GET /static/admin/simpleui-x/js/cookie.js?_=2025.06.24 HTTP/1.1" 200 524
INFO 2025-07-27 20:28:21,399 basehttp 18404 19536 "GET /static/admin/simpleui-x/theme/theme.js?_=2025.06.24 HTTP/1.1" 200 4274
INFO 2025-07-27 20:28:21,399 basehttp 18404 19536 "GET /static/admin/simpleui-x/theme/theme.js?_=2025.06.24 HTTP/1.1" 200 4274
INFO 2025-07-27 20:28:21,400 basehttp 18404 16132 "GET /static/admin/simpleui-x/waves/waves.min.js?_=2025.06.24 HTTP/1.1" 200 6329
INFO 2025-07-27 20:28:21,400 basehttp 18404 16132 "GET /static/admin/simpleui-x/waves/waves.min.js?_=2025.06.24 HTTP/1.1" 200 6329
INFO 2025-07-27 20:28:21,400 basehttp 18404 18968 "GET /static/admin/simpleui-x/js/menu.js?_=2025.06.24 HTTP/1.1" 200 1177
INFO 2025-07-27 20:28:21,400 basehttp 18404 18968 "GET /static/admin/simpleui-x/js/menu.js?_=2025.06.24 HTTP/1.1" 200 1177
INFO 2025-07-27 20:28:21,401 basehttp 18404 19536 "GET /static/admin/simpleui-x/js/index.js?_=2025.06.24 HTTP/1.1" 200 21589
INFO 2025-07-27 20:28:21,401 basehttp 18404 19536 "GET /static/admin/simpleui-x/js/index.js?_=2025.06.24 HTTP/1.1" 200 21589
INFO 2025-07-27 20:28:21,477 basehttp 18404 19536 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-regular-400.woff2 HTTP/1.1" 200 25096
INFO 2025-07-27 20:28:21,477 basehttp 18404 19536 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-regular-400.woff2 HTTP/1.1" 200 25096
INFO 2025-07-27 20:28:21,478 basehttp 18404 16132 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-brands-400.woff2 HTTP/1.1" 200 107460
INFO 2025-07-27 20:28:21,478 basehttp 18404 16132 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-brands-400.woff2 HTTP/1.1" 200 107460
INFO 2025-07-27 20:28:23,129 basehttp 18404 16132 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:28:23,129 basehttp 18404 16132 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:28:23,153 recommendations 18404 16132 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:28:23,196 basehttp 18404 16132 "GET /api/route-recommendations/?_=1753619303139 HTTP/1.1" 200 2265
INFO 2025-07-27 20:28:23,196 basehttp 18404 16132 "GET /api/route-recommendations/?_=1753619303139 HTTP/1.1" 200 2265
INFO 2025-07-27 20:28:23,739 basehttp 18404 16132 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:28:23,739 basehttp 18404 16132 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:28:23,762 recommendations 18404 16132 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:28:23,825 basehttp 18404 16132 "GET /api/route-recommendations/?_=1753619303750 HTTP/1.1" 200 2265
INFO 2025-07-27 20:28:23,825 basehttp 18404 16132 "GET /api/route-recommendations/?_=1753619303750 HTTP/1.1" 200 2265
INFO 2025-07-27 20:28:23,945 basehttp 18404 16132 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:28:23,945 basehttp 18404 16132 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:28:23,969 recommendations 18404 16132 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:28:24,034 basehttp 18404 16132 "GET /api/route-recommendations/?_=1753619303957 HTTP/1.1" 200 2265
INFO 2025-07-27 20:28:24,034 basehttp 18404 16132 "GET /api/route-recommendations/?_=1753619303957 HTTP/1.1" 200 2265
INFO 2025-07-27 20:28:24,703 basehttp 18404 16132 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:28:24,703 basehttp 18404 16132 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:28:24,761 basehttp 18404 16132 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2686
INFO 2025-07-27 20:28:24,761 basehttp 18404 16132 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2686
INFO 2025-07-27 20:28:25,545 basehttp 18404 16132 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:28:25,545 basehttp 18404 16132 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:28:26,661 basehttp 18404 16132 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:28:26,661 basehttp 18404 16132 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:28:27,195 basehttp 18404 16132 "GET /profile/ HTTP/1.1" 200 30606
INFO 2025-07-27 20:28:27,195 basehttp 18404 16132 "GET /profile/ HTTP/1.1" 200 30606
INFO 2025-07-27 20:28:29,311 basehttp 18404 16132 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:28:29,311 basehttp 18404 16132 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:28:53,523 basehttp 18404 16132 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:28:53,523 basehttp 18404 16132 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:28:53,557 basehttp 18404 19536 "GET /static/routes/js/analytics_overview.js HTTP/1.1" 200 21771
INFO 2025-07-27 20:28:53,557 basehttp 18404 19536 "GET /static/routes/js/analytics_overview.js HTTP/1.1" 200 21771
INFO 2025-07-27 20:28:53,557 basehttp 18404 18840 "GET /static/routes/js/analytics_spatial.js HTTP/1.1" 200 11670
INFO 2025-07-27 20:28:53,557 basehttp 18404 18840 "GET /static/routes/js/analytics_spatial.js HTTP/1.1" 200 11670
INFO 2025-07-27 20:28:53,558 basehttp 18404 18968 "GET /static/routes/js/analytics_temporal.js HTTP/1.1" 200 14874
INFO 2025-07-27 20:28:53,558 basehttp 18404 16956 "GET /static/routes/js/analytics_comparison.js HTTP/1.1" 200 23249
INFO 2025-07-27 20:28:53,558 basehttp 18404 18968 "GET /static/routes/js/analytics_temporal.js HTTP/1.1" 200 14874
INFO 2025-07-27 20:28:53,558 basehttp 18404 16956 "GET /static/routes/js/analytics_comparison.js HTTP/1.1" 200 23249
INFO 2025-07-27 20:28:53,559 basehttp 18404 16364 "GET /static/routes/js/analytics_temporal_spatial_comparison.js HTTP/1.1" 200 21888
INFO 2025-07-27 20:28:53,559 basehttp 18404 16364 "GET /static/routes/js/analytics_temporal_spatial_comparison.js HTTP/1.1" 200 21888
INFO 2025-07-27 20:28:53,562 basehttp 18404 16132 "GET /static/routes/js/echarts.min.js HTTP/1.1" 200 777872
INFO 2025-07-27 20:28:53,562 basehttp 18404 16132 "GET /static/routes/js/echarts.min.js HTTP/1.1" 200 777872
INFO 2025-07-27 20:28:53,957 basehttp 18404 16132 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:28:53,957 basehttp 18404 16132 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:28:53,997 basehttp 18404 18840 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:28:53,997 basehttp 18404 18840 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:28:53,998 basehttp 18404 16364 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:28:53,998 basehttp 18404 16364 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:28:53,998 basehttp 18404 18968 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:28:53,998 basehttp 18404 16956 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:28:53,998 basehttp 18404 18968 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:28:53,998 basehttp 18404 16956 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:28:55,050 basehttp 18404 16364 "GET /api/analytics/region_time_density/ HTTP/1.1" 200 12544
INFO 2025-07-27 20:28:55,050 basehttp 18404 16364 "GET /api/analytics/region_time_density/ HTTP/1.1" 200 12544
INFO 2025-07-27 20:28:55,051 basehttp 18404 18840 "GET /api/analytics/periodic_pattern_analysis/ HTTP/1.1" 200 306
INFO 2025-07-27 20:28:55,052 basehttp 18404 16956 "GET /api/analytics/time_space_heatmap/ HTTP/1.1" 200 1474
INFO 2025-07-27 20:28:55,051 basehttp 18404 18840 "GET /api/analytics/periodic_pattern_analysis/ HTTP/1.1" 200 306
INFO 2025-07-27 20:28:55,052 basehttp 18404 16956 "GET /api/analytics/time_space_heatmap/ HTTP/1.1" 200 1474
INFO 2025-07-27 20:28:55,181 basehttp 18404 18968 "GET /api/analytics/time_modes_comparison/ HTTP/1.1" 200 1957
INFO 2025-07-27 20:28:55,181 basehttp 18404 18968 "GET /api/analytics/time_modes_comparison/ HTTP/1.1" 200 1957
INFO 2025-07-27 20:28:56,212 basehttp 18404 16956 "GET /api/analytics/distance_time_relation/ HTTP/1.1" 200 241079
INFO 2025-07-27 20:28:56,212 basehttp 18404 18840 "GET /api/analytics/tag_analysis/ HTTP/1.1" 200 1227
INFO 2025-07-27 20:28:56,212 basehttp 18404 16956 "GET /api/analytics/distance_time_relation/ HTTP/1.1" 200 241079
INFO 2025-07-27 20:28:56,214 basehttp 18404 18968 "GET /api/analytics/mode_comparison/ HTTP/1.1" 200 452
INFO 2025-07-27 20:28:56,212 basehttp 18404 18840 "GET /api/analytics/tag_analysis/ HTTP/1.1" 200 1227
INFO 2025-07-27 20:28:56,214 basehttp 18404 18968 "GET /api/analytics/mode_comparison/ HTTP/1.1" 200 452
INFO 2025-07-27 20:28:56,918 basehttp 18404 16364 "GET /api/analytics/user_comparison/ HTTP/1.1" 200 1635
INFO 2025-07-27 20:28:56,918 basehttp 18404 16364 "GET /api/analytics/user_comparison/ HTTP/1.1" 200 1635
INFO 2025-07-27 20:28:58,401 basehttp 18404 16364 "GET /api/analytics/heatmap_data/ HTTP/1.1" 200 122290
INFO 2025-07-27 20:28:58,401 basehttp 18404 16364 "GET /api/analytics/heatmap_data/ HTTP/1.1" 200 122290
INFO 2025-07-27 20:28:58,730 basehttp 18404 18968 "GET /api/analytics/route_density_data/ HTTP/1.1" 200 2240258
INFO 2025-07-27 20:28:58,730 basehttp 18404 18968 "GET /api/analytics/route_density_data/ HTTP/1.1" 200 2240258
INFO 2025-07-27 20:29:00,108 basehttp 18404 16364 "GET /api/analytics/hourly_distribution/ HTTP/1.1" 200 340
INFO 2025-07-27 20:29:00,108 basehttp 18404 18968 "GET /api/analytics/daily_trend/ HTTP/1.1" 200 145
INFO 2025-07-27 20:29:00,108 basehttp 18404 16364 "GET /api/analytics/hourly_distribution/ HTTP/1.1" 200 340
INFO 2025-07-27 20:29:00,110 basehttp 18404 16956 "GET /api/analytics/monthly_trend/ HTTP/1.1" 200 72
INFO 2025-07-27 20:29:00,108 basehttp 18404 18968 "GET /api/analytics/daily_trend/ HTTP/1.1" 200 145
INFO 2025-07-27 20:29:00,110 basehttp 18404 16956 "GET /api/analytics/monthly_trend/ HTTP/1.1" 200 72
INFO 2025-07-27 20:29:02,471 basehttp 18404 18968 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:29:02,471 basehttp 18404 16956 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:29:02,471 basehttp 18404 18968 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:29:02,471 basehttp 18404 16364 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:29:02,471 basehttp 18404 16956 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:29:02,471 basehttp 18404 16364 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:29:02,472 basehttp 18404 18840 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:29:02,472 basehttp 18404 18840 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:29:05,504 basehttp 18404 18840 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:29:05,504 basehttp 18404 18840 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:29:06,642 basehttp 18404 18840 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:29:06,642 basehttp 18404 18840 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:29:24,058 basehttp 18404 18840 "GET /admin/routes/routesearch/ HTTP/1.1" 200 63770
INFO 2025-07-27 20:29:24,058 basehttp 18404 18840 "GET /admin/routes/routesearch/ HTTP/1.1" 200 63770
INFO 2025-07-27 20:29:24,078 basehttp 18404 19536 "GET /static/admin/simpleui-x/js/vue.min.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,078 basehttp 18404 18968 "GET /static/admin/simpleui-x/elementui/theme-chalk/index.css HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,078 basehttp 18404 16132 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/css/all.min.css HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,078 basehttp 18404 19536 "GET /static/admin/simpleui-x/js/vue.min.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,078 basehttp 18404 18968 "GET /static/admin/simpleui-x/elementui/theme-chalk/index.css HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,078 basehttp 18404 16132 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/css/all.min.css HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,079 basehttp 18404 16364 "GET /static/admin/simpleui-x/css/base.css?_=2.7 HTTP/1.1" 200 4867
INFO 2025-07-27 20:29:24,079 basehttp 18404 16956 "GET /static/admin/simpleui-x/theme/simpleui.css HTTP/1.1" 200 4270
INFO 2025-07-27 20:29:24,080 basehttp 18404 18840 "GET /static/admin/css/base.css?_=2.7 HTTP/1.1" 200 16307
INFO 2025-07-27 20:29:24,079 basehttp 18404 16364 "GET /static/admin/simpleui-x/css/base.css?_=2.7 HTTP/1.1" 200 4867
INFO 2025-07-27 20:29:24,079 basehttp 18404 16956 "GET /static/admin/simpleui-x/theme/simpleui.css HTTP/1.1" 200 4270
INFO 2025-07-27 20:29:24,080 basehttp 18404 18840 "GET /static/admin/css/base.css?_=2.7 HTTP/1.1" 200 16307
INFO 2025-07-27 20:29:24,083 basehttp 18404 16132 "GET /static/admin/simpleui-x/elementui/index.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,083 basehttp 18404 16132 "GET /static/admin/simpleui-x/elementui/index.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,085 basehttp 18404 19536 "GET /static/admin/css/changelists.css?_=20180905 HTTP/1.1" 200 6302
INFO 2025-07-27 20:29:24,085 basehttp 18404 19536 "GET /static/admin/css/changelists.css?_=20180905 HTTP/1.1" 200 6302
INFO 2025-07-27 20:29:24,086 basehttp 18404 16956 "GET /static/admin/simpleui-x/elementui/umd/locale/en.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,086 basehttp 18404 16956 "GET /static/admin/simpleui-x/elementui/umd/locale/en.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,086 basehttp 18404 18968 "GET /static/admin/css/responsive.css?_=20180905 HTTP/1.1" 200 18344
INFO 2025-07-27 20:29:24,086 basehttp 18404 18968 "GET /static/admin/css/responsive.css?_=20180905 HTTP/1.1" 200 18344
INFO 2025-07-27 20:29:24,088 basehttp 18404 16364 "GET /static/admin/simpleui-x/js/language.js?_=2.1.2 HTTP/1.1" 200 189
INFO 2025-07-27 20:29:24,088 basehttp 18404 18840 "GET /static/admin/simpleui-x/locale/en-us.js?_=2.1.2 HTTP/1.1" 200 811
INFO 2025-07-27 20:29:24,088 basehttp 18404 16364 "GET /static/admin/simpleui-x/js/language.js?_=2.1.2 HTTP/1.1" 200 189
INFO 2025-07-27 20:29:24,088 basehttp 18404 18840 "GET /static/admin/simpleui-x/locale/en-us.js?_=2.1.2 HTTP/1.1" 200 811
INFO 2025-07-27 20:29:24,089 basehttp 18404 16132 "GET /static/admin/css/fonts.css HTTP/1.1" 200 423
INFO 2025-07-27 20:29:24,089 basehttp 18404 16132 "GET /static/admin/css/fonts.css HTTP/1.1" 200 423
INFO 2025-07-27 20:29:24,092 basehttp 18404 19536 "GET /static/admin/simpleui-x/locale/zh-hans.js HTTP/1.1" 200 1254
INFO 2025-07-27 20:29:24,092 basehttp 18404 19536 "GET /static/admin/simpleui-x/locale/zh-hans.js HTTP/1.1" 200 1254
INFO 2025-07-27 20:29:24,096 basehttp 18404 18968 "GET /static/admin/simpleui-x/js/cookie.js HTTP/1.1" 200 524
INFO 2025-07-27 20:29:24,096 basehttp 18404 18968 "GET /static/admin/simpleui-x/js/cookie.js HTTP/1.1" 200 524
INFO 2025-07-27 20:29:24,097 basehttp 18404 18840 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-07-27 20:29:24,098 basehttp 18404 16956 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:29:24,097 basehttp 18404 18840 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-07-27 20:29:24,098 basehttp 18404 16956 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:29:24,098 basehttp 18404 16132 "GET /static/admin/js/core.js HTTP/1.1" 200 5418
INFO 2025-07-27 20:29:24,099 basehttp 18404 19536 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 6078
INFO 2025-07-27 20:29:24,098 basehttp 18404 16132 "GET /static/admin/js/core.js HTTP/1.1" 200 5418
INFO 2025-07-27 20:29:24,099 basehttp 18404 19536 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 6078
INFO 2025-07-27 20:29:24,100 basehttp 18404 18968 "GET /static/admin/js/actions.js HTTP/1.1" 200 6783
INFO 2025-07-27 20:29:24,100 basehttp 18404 18968 "GET /static/admin/js/actions.js HTTP/1.1" 200 6783
INFO 2025-07-27 20:29:24,103 basehttp 18404 18840 "GET /static/admin/js/urlify.js HTTP/1.1" 200 8632
INFO 2025-07-27 20:29:24,103 basehttp 18404 18840 "GET /static/admin/js/urlify.js HTTP/1.1" 200 8632
INFO 2025-07-27 20:29:24,103 basehttp 18404 16956 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-07-27 20:29:24,103 basehttp 18404 16956 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-07-27 20:29:24,106 basehttp 18404 18968 "GET /static/admin/simpleui-x/automatic/dicts.js HTTP/1.1" 200 12677
INFO 2025-07-27 20:29:24,106 basehttp 18404 18968 "GET /static/admin/simpleui-x/automatic/dicts.js HTTP/1.1" 200 12677
INFO 2025-07-27 20:29:24,107 basehttp 18404 18840 "GET /static/admin/simpleui-x/automatic/segment.js HTTP/1.1" 200 1194
INFO 2025-07-27 20:29:24,107 basehttp 18404 18840 "GET /static/admin/simpleui-x/automatic/segment.js HTTP/1.1" 200 1194
INFO 2025-07-27 20:29:24,107 basehttp 18404 16956 "GET /static/admin/simpleui-x/js/axios.min.js HTTP/1.1" 200 14237
INFO 2025-07-27 20:29:24,107 basehttp 18404 16956 "GET /static/admin/simpleui-x/js/axios.min.js HTTP/1.1" 200 14237
INFO 2025-07-27 20:29:24,109 basehttp 18404 19536 "GET /static/admin/js/vendor/jquery/jquery.min.js HTTP/1.1" 200 89476
INFO 2025-07-27 20:29:24,109 basehttp 18404 19536 "GET /static/admin/js/vendor/jquery/jquery.min.js HTTP/1.1" 200 89476
INFO 2025-07-27 20:29:24,109 basehttp 18404 18968 "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
INFO 2025-07-27 20:29:24,109 basehttp 18404 18968 "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
INFO 2025-07-27 20:29:24,110 basehttp 18404 16364 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 287630
INFO 2025-07-27 20:29:24,110 basehttp 18404 16364 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 287630
INFO 2025-07-27 20:29:24,111 basehttp 18404 16132 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
INFO 2025-07-27 20:29:24,111 basehttp 18404 16132 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
INFO 2025-07-27 20:29:24,114 basehttp 18404 16132 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-07-27 20:29:24,114 basehttp 18404 16132 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-07-27 20:29:24,140 basehttp 18404 16132 "GET /static/admin/simpleui-x/elementui/theme-chalk/fonts/element-icons.woff HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,140 basehttp 18404 16132 "GET /static/admin/simpleui-x/elementui/theme-chalk/fonts/element-icons.woff HTTP/1.1" 304 0
INFO 2025-07-27 20:29:24,147 basehttp 18404 16132 "GET /static/admin/fonts/Roboto-Regular-webfont.woff HTTP/1.1" 200 85876
INFO 2025-07-27 20:29:24,147 basehttp 18404 16132 "GET /static/admin/fonts/Roboto-Regular-webfont.woff HTTP/1.1" 200 85876
INFO 2025-07-27 20:29:24,172 basehttp 18404 16132 "GET /static/admin/fonts/Roboto-Bold-webfont.woff HTTP/1.1" 200 86184
INFO 2025-07-27 20:29:24,172 basehttp 18404 16132 "GET /static/admin/fonts/Roboto-Bold-webfont.woff HTTP/1.1" 200 86184
INFO 2025-07-27 20:29:24,176 basehttp 18404 16364 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-07-27 20:29:24,176 basehttp 18404 16364 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-07-27 20:29:24,188 basehttp 18404 16364 "GET /static/admin/fonts/Roboto-Light-webfont.woff HTTP/1.1" 200 85692
INFO 2025-07-27 20:29:24,188 basehttp 18404 16364 "GET /static/admin/fonts/Roboto-Light-webfont.woff HTTP/1.1" 200 85692
INFO 2025-07-27 20:29:25,145 basehttp 18404 16364 "GET /admin/routes/routerecord/ HTTP/1.1" 200 62313
INFO 2025-07-27 20:29:25,145 basehttp 18404 16364 "GET /admin/routes/routerecord/ HTTP/1.1" 200 62313
INFO 2025-07-27 20:29:25,160 basehttp 18404 16364 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:29:25,160 basehttp 18404 16364 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:31:00,260 autoreload 18404 14592 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\models.py changed, reloading.
INFO 2025-07-27 20:31:00,260 autoreload 18404 14592 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\models.py changed, reloading.
INFO 2025-07-27 20:31:01,016 autoreload 15880 15596 Watching for file changes with StatReloader
INFO 2025-07-27 20:31:01,016 autoreload 15880 15596 Watching for file changes with StatReloader
INFO 2025-07-27 20:31:16,400 autoreload 15880 15596 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\models.py changed, reloading.
INFO 2025-07-27 20:31:16,400 autoreload 15880 15596 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\models.py changed, reloading.
INFO 2025-07-27 20:31:17,215 autoreload 14440 14420 Watching for file changes with StatReloader
INFO 2025-07-27 20:31:17,215 autoreload 14440 14420 Watching for file changes with StatReloader
INFO 2025-07-27 20:31:31,642 autoreload 14440 14420 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\admin.py changed, reloading.
INFO 2025-07-27 20:31:31,642 autoreload 14440 14420 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\admin.py changed, reloading.
INFO 2025-07-27 20:31:32,321 autoreload 19144 20472 Watching for file changes with StatReloader
INFO 2025-07-27 20:31:32,321 autoreload 19144 20472 Watching for file changes with StatReloader
INFO 2025-07-27 20:31:56,792 autoreload 19144 20472 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\admin.py changed, reloading.
INFO 2025-07-27 20:31:56,792 autoreload 19144 20472 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\admin.py changed, reloading.
INFO 2025-07-27 20:31:57,512 autoreload 17648 17692 Watching for file changes with StatReloader
INFO 2025-07-27 20:31:57,512 autoreload 17648 17692 Watching for file changes with StatReloader
INFO 2025-07-27 20:32:18,066 autoreload 17648 17692 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\admin.py changed, reloading.
INFO 2025-07-27 20:32:18,066 autoreload 17648 17692 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\admin.py changed, reloading.
INFO 2025-07-27 20:32:18,951 autoreload 1428 18668 Watching for file changes with StatReloader
INFO 2025-07-27 20:32:18,951 autoreload 1428 18668 Watching for file changes with StatReloader
INFO 2025-07-27 20:33:23,983 autoreload 1428 18668 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\migrations\0001_initial.py changed, reloading.
INFO 2025-07-27 20:33:23,983 autoreload 1428 18668 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\migrations\0001_initial.py changed, reloading.
INFO 2025-07-27 20:33:24,778 autoreload 14940 14988 Watching for file changes with StatReloader
INFO 2025-07-27 20:33:24,778 autoreload 14940 14988 Watching for file changes with StatReloader
INFO 2025-07-27 20:33:58,861 autoreload 1080 13852 Watching for file changes with StatReloader
INFO 2025-07-27 20:33:58,861 autoreload 1080 13852 Watching for file changes with StatReloader
INFO 2025-07-27 20:38:44,021 autoreload 16180 812 Watching for file changes with StatReloader
INFO 2025-07-27 20:38:44,021 autoreload 16180 812 Watching for file changes with StatReloader
INFO 2025-07-27 20:39:30,402 autoreload 16180 812 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\models.py changed, reloading.
INFO 2025-07-27 20:39:30,402 autoreload 16180 812 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\models.py changed, reloading.
INFO 2025-07-27 20:39:31,165 autoreload 13684 4160 Watching for file changes with StatReloader
INFO 2025-07-27 20:39:31,165 autoreload 13684 4160 Watching for file changes with StatReloader
INFO 2025-07-27 20:39:45,488 autoreload 13684 4160 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\admin.py changed, reloading.
INFO 2025-07-27 20:39:45,488 autoreload 13684 4160 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\admin.py changed, reloading.
INFO 2025-07-27 20:39:46,428 autoreload 13136 11012 Watching for file changes with StatReloader
INFO 2025-07-27 20:39:46,428 autoreload 13136 11012 Watching for file changes with StatReloader
INFO 2025-07-27 20:40:47,092 autoreload 13136 11012 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\migrations\0001_initial.py changed, reloading.
INFO 2025-07-27 20:40:47,092 autoreload 13136 11012 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\migrations\0001_initial.py changed, reloading.
INFO 2025-07-27 20:40:48,102 autoreload 17144 19088 Watching for file changes with StatReloader
INFO 2025-07-27 20:40:48,102 autoreload 17144 19088 Watching for file changes with StatReloader
INFO 2025-07-27 20:40:58,130 autoreload 1920 19596 Watching for file changes with StatReloader
INFO 2025-07-27 20:40:58,130 autoreload 1920 19596 Watching for file changes with StatReloader
INFO 2025-07-27 20:42:31,858 basehttp 17144 7660 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:42:31,858 basehttp 17144 7660 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:42:31,940 recommendations 17144 7660 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:42:32,004 basehttp 17144 7660 "GET /api/route-recommendations/?_=1753620151929 HTTP/1.1" 200 2283
INFO 2025-07-27 20:42:32,004 basehttp 17144 7660 "GET /api/route-recommendations/?_=1753620151929 HTTP/1.1" 200 2283
INFO 2025-07-27 20:42:32,822 basehttp 17144 7660 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:42:32,822 basehttp 17144 7660 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:42:32,883 basehttp 17144 15300 "GET /static/routes/js/analytics_overview.js HTTP/1.1" 200 21771
INFO 2025-07-27 20:42:32,883 basehttp 17144 15300 "GET /static/routes/js/analytics_overview.js HTTP/1.1" 200 21771
INFO 2025-07-27 20:42:32,884 basehttp 17144 14572 "GET /static/routes/js/analytics_temporal.js HTTP/1.1" 200 14874
INFO 2025-07-27 20:42:32,884 basehttp 17144 14572 "GET /static/routes/js/analytics_temporal.js HTTP/1.1" 200 14874
INFO 2025-07-27 20:42:32,886 basehttp 17144 15300 "GET /static/routes/js/analytics_spatial.js HTTP/1.1" 200 11670
INFO 2025-07-27 20:42:32,886 basehttp 17144 15300 "GET /static/routes/js/analytics_spatial.js HTTP/1.1" 200 11670
INFO 2025-07-27 20:42:32,888 basehttp 17144 14572 "GET /static/routes/js/analytics_comparison.js HTTP/1.1" 200 23249
INFO 2025-07-27 20:42:32,888 basehttp 17144 14572 "GET /static/routes/js/analytics_comparison.js HTTP/1.1" 200 23249
INFO 2025-07-27 20:42:32,889 basehttp 17144 15300 "GET /static/routes/js/analytics_temporal_spatial_comparison.js HTTP/1.1" 200 21888
INFO 2025-07-27 20:42:32,889 basehttp 17144 15300 "GET /static/routes/js/analytics_temporal_spatial_comparison.js HTTP/1.1" 200 21888
INFO 2025-07-27 20:42:32,892 basehttp 17144 7660 "GET /static/routes/js/echarts.min.js HTTP/1.1" 200 777872
INFO 2025-07-27 20:42:32,892 basehttp 17144 7660 "GET /static/routes/js/echarts.min.js HTTP/1.1" 200 777872
INFO 2025-07-27 20:42:33,075 basehttp 17144 14572 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:42:33,075 basehttp 17144 14572 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:42:33,089 basehttp 17144 7660 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:42:33,089 basehttp 17144 7660 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:42:33,089 basehttp 17144 15300 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:42:33,089 basehttp 17144 15300 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:42:33,191 basehttp 17144 14572 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:42:33,191 basehttp 17144 14572 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:42:33,192 basehttp 17144 7660 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:42:33,192 basehttp 17144 7660 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:42:34,261 basehttp 17144 7660 "GET /api/analytics/mode_comparison/ HTTP/1.1" 200 452
INFO 2025-07-27 20:42:34,261 basehttp 17144 7660 "GET /api/analytics/mode_comparison/ HTTP/1.1" 200 452
INFO 2025-07-27 20:42:34,265 basehttp 17144 15300 "GET /api/analytics/tag_analysis/ HTTP/1.1" 200 1227
INFO 2025-07-27 20:42:34,265 basehttp 17144 14572 "GET /api/analytics/distance_time_relation/ HTTP/1.1" 200 241079
INFO 2025-07-27 20:42:34,265 basehttp 17144 15300 "GET /api/analytics/tag_analysis/ HTTP/1.1" 200 1227
INFO 2025-07-27 20:42:34,265 basehttp 17144 14572 "GET /api/analytics/distance_time_relation/ HTTP/1.1" 200 241079
INFO 2025-07-27 20:42:34,841 basehttp 17144 16108 "GET /api/analytics/periodic_pattern_analysis/ HTTP/1.1" 200 306
INFO 2025-07-27 20:42:34,842 basehttp 17144 14572 "GET /api/analytics/time_space_heatmap/ HTTP/1.1" 200 1474
INFO 2025-07-27 20:42:34,841 basehttp 17144 16108 "GET /api/analytics/periodic_pattern_analysis/ HTTP/1.1" 200 306
INFO 2025-07-27 20:42:34,842 basehttp 17144 14572 "GET /api/analytics/time_space_heatmap/ HTTP/1.1" 200 1474
INFO 2025-07-27 20:42:34,844 basehttp 17144 7660 "GET /api/analytics/region_time_density/ HTTP/1.1" 200 12544
INFO 2025-07-27 20:42:34,844 basehttp 17144 7660 "GET /api/analytics/region_time_density/ HTTP/1.1" 200 12544
INFO 2025-07-27 20:42:34,929 basehttp 17144 15300 "GET /api/analytics/time_modes_comparison/ HTTP/1.1" 200 1957
INFO 2025-07-27 20:42:34,929 basehttp 17144 15300 "GET /api/analytics/time_modes_comparison/ HTTP/1.1" 200 1957
INFO 2025-07-27 20:42:34,973 basehttp 17144 16840 "GET /api/analytics/user_comparison/ HTTP/1.1" 200 1635
INFO 2025-07-27 20:42:34,973 basehttp 17144 16840 "GET /api/analytics/user_comparison/ HTTP/1.1" 200 1635
INFO 2025-07-27 20:42:37,428 basehttp 17144 16840 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:42:37,428 basehttp 17144 16840 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:42:37,462 recommendations 17144 16840 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:42:37,527 basehttp 17144 16840 "GET /api/route-recommendations/?_=1753620157439 HTTP/1.1" 200 2283
INFO 2025-07-27 20:42:37,527 basehttp 17144 16840 "GET /api/route-recommendations/?_=1753620157439 HTTP/1.1" 200 2283
INFO 2025-07-27 20:42:38,031 basehttp 17144 16840 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:42:38,031 basehttp 17144 16840 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:42:38,077 basehttp 17144 15300 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:42:38,077 basehttp 17144 15300 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:42:38,080 basehttp 17144 16840 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:42:38,080 basehttp 17144 16840 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:42:42,699 basehttp 17144 16840 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:42:42,699 basehttp 17144 16840 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:42:43,854 basehttp 17144 16840 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:42:43,854 basehttp 17144 16840 "GET /api/route-history/ HTTP/1.1" 200 6483
INFO 2025-07-27 20:42:46,413 basehttp 17144 16840 "GET /api/route-detail/1205/ HTTP/1.1" 200 59472
INFO 2025-07-27 20:42:46,413 basehttp 17144 16840 "GET /api/route-detail/1205/ HTTP/1.1" 200 59472
INFO 2025-07-27 20:42:47,568 basehttp 17144 16840 "POST /api/route-planning/ HTTP/1.1" 200 59168
INFO 2025-07-27 20:42:47,568 basehttp 17144 16840 "POST /api/route-planning/ HTTP/1.1" 200 59168
INFO 2025-07-27 20:42:47,589 basehttp 17144 16840 "GET /api/route-history/ HTTP/1.1" 200 6434
INFO 2025-07-27 20:42:47,589 basehttp 17144 16840 "GET /api/route-history/ HTTP/1.1" 200 6434
INFO 2025-07-27 20:42:52,424 basehttp 17144 16840 "POST /api/route-planning/ HTTP/1.1" 200 117185
INFO 2025-07-27 20:42:52,424 basehttp 17144 16840 "POST /api/route-planning/ HTTP/1.1" 200 117185
INFO 2025-07-27 20:42:52,441 basehttp 17144 16840 "GET /api/route-history/ HTTP/1.1" 200 6386
INFO 2025-07-27 20:42:52,441 basehttp 17144 16840 "GET /api/route-history/ HTTP/1.1" 200 6386
INFO 2025-07-27 20:42:53,799 basehttp 17144 16840 "POST /api/route-planning/ HTTP/1.1" 200 41410
INFO 2025-07-27 20:42:53,799 basehttp 17144 16840 "POST /api/route-planning/ HTTP/1.1" 200 41410
INFO 2025-07-27 20:42:53,813 basehttp 17144 16840 "GET /api/route-history/ HTTP/1.1" 200 6339
INFO 2025-07-27 20:42:53,813 basehttp 17144 16840 "GET /api/route-history/ HTTP/1.1" 200 6339
INFO 2025-07-27 20:43:04,527 basehttp 17144 16840 "GET /admin/ HTTP/1.1" 200 19121
INFO 2025-07-27 20:43:04,527 basehttp 17144 16840 "GET /admin/ HTTP/1.1" 200 19121
INFO 2025-07-27 20:43:04,560 basehttp 17144 16108 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/css/all.min.css HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,560 basehttp 17144 16840 "GET /static/admin/simpleui-x/elementui/theme-chalk/index.css HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,560 basehttp 17144 14572 "GET /static/admin/simpleui-x/img/logo.png HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,561 basehttp 17144 19076 "GET /static/admin/simpleui-x/js/vue.min.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,560 basehttp 17144 16108 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/css/all.min.css HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,560 basehttp 17144 16840 "GET /static/admin/simpleui-x/elementui/theme-chalk/index.css HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,560 basehttp 17144 14572 "GET /static/admin/simpleui-x/img/logo.png HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,561 basehttp 17144 7660 "GET /static/admin/simpleui-x/waves/waves.min.css?_=2.1 HTTP/1.1" 200 3861
INFO 2025-07-27 20:43:04,562 basehttp 17144 15300 "GET /static/admin/simpleui-x/css/index.css?_=3.2 HTTP/1.1" 200 6994
INFO 2025-07-27 20:43:04,561 basehttp 17144 19076 "GET /static/admin/simpleui-x/js/vue.min.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,561 basehttp 17144 7660 "GET /static/admin/simpleui-x/waves/waves.min.css?_=2.1 HTTP/1.1" 200 3861
INFO 2025-07-27 20:43:04,562 basehttp 17144 15300 "GET /static/admin/simpleui-x/css/index.css?_=3.2 HTTP/1.1" 200 6994
INFO 2025-07-27 20:43:04,577 basehttp 17144 14572 "GET /static/admin/simpleui-x/elementui/umd/locale/en.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,578 basehttp 17144 7660 "GET /static/admin/simpleui-x/elementui/index.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,577 basehttp 17144 14572 "GET /static/admin/simpleui-x/elementui/umd/locale/en.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,578 basehttp 17144 7660 "GET /static/admin/simpleui-x/elementui/index.js?_=2025.06.24 HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,579 basehttp 17144 19076 "GET /static/admin/simpleui-x/automatic/segment.js?_=2025.06.24 HTTP/1.1" 200 1194
INFO 2025-07-27 20:43:04,579 basehttp 17144 16840 "GET /static/admin/simpleui-x/locale/en-us.js?_=2025.06.24 HTTP/1.1" 200 811
INFO 2025-07-27 20:43:04,579 basehttp 17144 15300 "GET /static/admin/simpleui-x/automatic/dicts.js?_=2025.06.24 HTTP/1.1" 200 12677
INFO 2025-07-27 20:43:04,579 basehttp 17144 19076 "GET /static/admin/simpleui-x/automatic/segment.js?_=2025.06.24 HTTP/1.1" 200 1194
INFO 2025-07-27 20:43:04,579 basehttp 17144 16108 "GET /static/admin/simpleui-x/locale/zh-hans.js?_=2025.06.24 HTTP/1.1" 200 1254
INFO 2025-07-27 20:43:04,579 basehttp 17144 16840 "GET /static/admin/simpleui-x/locale/en-us.js?_=2025.06.24 HTTP/1.1" 200 811
INFO 2025-07-27 20:43:04,579 basehttp 17144 15300 "GET /static/admin/simpleui-x/automatic/dicts.js?_=2025.06.24 HTTP/1.1" 200 12677
INFO 2025-07-27 20:43:04,579 basehttp 17144 16108 "GET /static/admin/simpleui-x/locale/zh-hans.js?_=2025.06.24 HTTP/1.1" 200 1254
INFO 2025-07-27 20:43:04,618 basehttp 17144 16840 "GET /static/admin/simpleui-x/js/cookie.js?_=2025.06.24 HTTP/1.1" 200 524
INFO 2025-07-27 20:43:04,618 basehttp 17144 16840 "GET /static/admin/simpleui-x/js/cookie.js?_=2025.06.24 HTTP/1.1" 200 524
INFO 2025-07-27 20:43:04,619 basehttp 17144 19076 "GET /static/admin/simpleui-x/waves/waves.min.js?_=2025.06.24 HTTP/1.1" 200 6329
INFO 2025-07-27 20:43:04,619 basehttp 17144 19076 "GET /static/admin/simpleui-x/waves/waves.min.js?_=2025.06.24 HTTP/1.1" 200 6329
INFO 2025-07-27 20:43:04,619 basehttp 17144 16108 "GET /static/admin/simpleui-x/theme/theme.js?_=2025.06.24 HTTP/1.1" 200 4274
INFO 2025-07-27 20:43:04,619 basehttp 17144 16108 "GET /static/admin/simpleui-x/theme/theme.js?_=2025.06.24 HTTP/1.1" 200 4274
INFO 2025-07-27 20:43:04,621 basehttp 17144 15300 "GET /static/admin/simpleui-x/js/menu.js?_=2025.06.24 HTTP/1.1" 200 1177
INFO 2025-07-27 20:43:04,621 basehttp 17144 15300 "GET /static/admin/simpleui-x/js/menu.js?_=2025.06.24 HTTP/1.1" 200 1177
INFO 2025-07-27 20:43:04,622 basehttp 17144 7660 "GET /static/admin/simpleui-x/js/index.js?_=2025.06.24 HTTP/1.1" 200 21589
INFO 2025-07-27 20:43:04,622 basehttp 17144 7660 "GET /static/admin/simpleui-x/js/index.js?_=2025.06.24 HTTP/1.1" 200 21589
INFO 2025-07-27 20:43:04,676 basehttp 17144 19076 "GET /static/admin/simpleui-x/elementui/theme-chalk/fonts/element-icons.woff HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,676 basehttp 17144 19076 "GET /static/admin/simpleui-x/elementui/theme-chalk/fonts/element-icons.woff HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,676 basehttp 17144 16108 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-solid-900.woff2 HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,676 basehttp 17144 16108 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-solid-900.woff2 HTTP/1.1" 304 0
INFO 2025-07-27 20:43:04,677 basehttp 17144 7660 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-regular-400.woff2 HTTP/1.1" 200 25096
INFO 2025-07-27 20:43:04,677 basehttp 17144 7660 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-regular-400.woff2 HTTP/1.1" 200 25096
INFO 2025-07-27 20:43:04,678 basehttp 17144 15300 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-brands-400.woff2 HTTP/1.1" 200 107460
INFO 2025-07-27 20:43:04,678 basehttp 17144 15300 "GET /static/admin/simpleui-x/fontawesome-free-6.2.0-web/webfonts/fa-brands-400.woff2 HTTP/1.1" 200 107460
INFO 2025-07-27 20:43:06,327 basehttp 17144 15300 "GET /admin/routes/routesearch/ HTTP/1.1" 200 63752
INFO 2025-07-27 20:43:06,327 basehttp 17144 15300 "GET /admin/routes/routesearch/ HTTP/1.1" 200 63752
INFO 2025-07-27 20:43:06,341 basehttp 17144 15300 "GET /static/admin/css/base.css?_=2.7 HTTP/1.1" 200 16307
INFO 2025-07-27 20:43:06,341 basehttp 17144 15300 "GET /static/admin/css/base.css?_=2.7 HTTP/1.1" 200 16307
INFO 2025-07-27 20:43:06,341 basehttp 17144 7660 "GET /static/admin/simpleui-x/css/base.css?_=2.7 HTTP/1.1" 200 4867
INFO 2025-07-27 20:43:06,341 basehttp 17144 7660 "GET /static/admin/simpleui-x/css/base.css?_=2.7 HTTP/1.1" 200 4867
INFO 2025-07-27 20:43:06,344 basehttp 17144 16108 "GET /static/admin/simpleui-x/theme/simpleui.css HTTP/1.1" 200 4270
INFO 2025-07-27 20:43:06,344 basehttp 17144 19076 "GET /static/admin/simpleui-x/js/language.js?_=2.1.2 HTTP/1.1" 200 189
INFO 2025-07-27 20:43:06,344 basehttp 17144 16108 "GET /static/admin/simpleui-x/theme/simpleui.css HTTP/1.1" 200 4270
INFO 2025-07-27 20:43:06,344 basehttp 17144 19076 "GET /static/admin/simpleui-x/js/language.js?_=2.1.2 HTTP/1.1" 200 189
INFO 2025-07-27 20:43:06,347 basehttp 17144 16840 "GET /static/admin/simpleui-x/locale/en-us.js?_=2.1.2 HTTP/1.1" 200 811
INFO 2025-07-27 20:43:06,347 basehttp 17144 16840 "GET /static/admin/simpleui-x/locale/en-us.js?_=2.1.2 HTTP/1.1" 200 811
INFO 2025-07-27 20:43:06,347 basehttp 17144 15300 "GET /static/admin/simpleui-x/locale/zh-hans.js HTTP/1.1" 200 1254
INFO 2025-07-27 20:43:06,347 basehttp 17144 15300 "GET /static/admin/simpleui-x/locale/zh-hans.js HTTP/1.1" 200 1254
INFO 2025-07-27 20:43:06,348 basehttp 17144 14572 "GET /static/admin/simpleui-x/js/cookie.js HTTP/1.1" 200 524
INFO 2025-07-27 20:43:06,348 basehttp 17144 14572 "GET /static/admin/simpleui-x/js/cookie.js HTTP/1.1" 200 524
INFO 2025-07-27 20:43:06,349 basehttp 17144 7660 "GET /static/admin/css/changelists.css?_=20180905 HTTP/1.1" 200 6302
INFO 2025-07-27 20:43:06,349 basehttp 17144 7660 "GET /static/admin/css/changelists.css?_=20180905 HTTP/1.1" 200 6302
INFO 2025-07-27 20:43:06,353 basehttp 17144 19076 "GET /static/admin/css/responsive.css?_=20180905 HTTP/1.1" 200 18344
INFO 2025-07-27 20:43:06,353 basehttp 17144 19076 "GET /static/admin/css/responsive.css?_=20180905 HTTP/1.1" 200 18344
INFO 2025-07-27 20:43:06,355 basehttp 17144 14572 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-07-27 20:43:06,356 basehttp 17144 16840 "GET /static/admin/css/fonts.css HTTP/1.1" 200 423
INFO 2025-07-27 20:43:06,355 basehttp 17144 14572 "GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
INFO 2025-07-27 20:43:06,356 basehttp 17144 16840 "GET /static/admin/css/fonts.css HTTP/1.1" 200 423
INFO 2025-07-27 20:43:06,357 basehttp 17144 7660 "GET /static/admin/js/core.js HTTP/1.1" 200 5418
INFO 2025-07-27 20:43:06,357 basehttp 17144 7660 "GET /static/admin/js/core.js HTTP/1.1" 200 5418
INFO 2025-07-27 20:43:06,362 basehttp 17144 15300 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:43:06,362 basehttp 17144 15300 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:43:06,362 basehttp 17144 19076 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 6078
INFO 2025-07-27 20:43:06,362 basehttp 17144 19076 "GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 6078
INFO 2025-07-27 20:43:06,364 basehttp 17144 14572 "GET /static/admin/js/actions.js HTTP/1.1" 200 6783
INFO 2025-07-27 20:43:06,364 basehttp 17144 14572 "GET /static/admin/js/actions.js HTTP/1.1" 200 6783
INFO 2025-07-27 20:43:06,365 basehttp 17144 7660 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-07-27 20:43:06,366 basehttp 17144 16840 "GET /static/admin/js/urlify.js HTTP/1.1" 200 8632
INFO 2025-07-27 20:43:06,365 basehttp 17144 7660 "GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
INFO 2025-07-27 20:43:06,366 basehttp 17144 16840 "GET /static/admin/js/urlify.js HTTP/1.1" 200 8632
INFO 2025-07-27 20:43:06,368 basehttp 17144 14572 "GET /static/admin/simpleui-x/automatic/dicts.js HTTP/1.1" 200 12677
INFO 2025-07-27 20:43:06,368 basehttp 17144 14572 "GET /static/admin/simpleui-x/automatic/dicts.js HTTP/1.1" 200 12677
INFO 2025-07-27 20:43:06,369 basehttp 17144 16840 "GET /static/admin/simpleui-x/automatic/segment.js HTTP/1.1" 200 1194
INFO 2025-07-27 20:43:06,369 basehttp 17144 16840 "GET /static/admin/simpleui-x/automatic/segment.js HTTP/1.1" 200 1194
INFO 2025-07-27 20:43:06,370 basehttp 17144 7660 "GET /static/admin/simpleui-x/js/axios.min.js HTTP/1.1" 200 14237
INFO 2025-07-27 20:43:06,370 basehttp 17144 7660 "GET /static/admin/simpleui-x/js/axios.min.js HTTP/1.1" 200 14237
INFO 2025-07-27 20:43:06,371 basehttp 17144 15300 "GET /static/admin/js/vendor/jquery/jquery.min.js HTTP/1.1" 200 89476
INFO 2025-07-27 20:43:06,371 basehttp 17144 15300 "GET /static/admin/js/vendor/jquery/jquery.min.js HTTP/1.1" 200 89476
INFO 2025-07-27 20:43:06,372 basehttp 17144 14572 "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
INFO 2025-07-27 20:43:06,372 basehttp 17144 14572 "GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
INFO 2025-07-27 20:43:06,372 basehttp 17144 16108 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 287630
INFO 2025-07-27 20:43:06,372 basehttp 17144 16108 "GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" 200 287630
INFO 2025-07-27 20:43:06,373 basehttp 17144 19076 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
INFO 2025-07-27 20:43:06,373 basehttp 17144 19076 "GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" 200 232381
INFO 2025-07-27 20:43:06,381 basehttp 17144 19076 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-07-27 20:43:06,381 basehttp 17144 19076 "GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
INFO 2025-07-27 20:43:06,411 basehttp 17144 19076 "GET /static/admin/fonts/Roboto-Regular-webfont.woff HTTP/1.1" 200 85876
INFO 2025-07-27 20:43:06,411 basehttp 17144 19076 "GET /static/admin/fonts/Roboto-Regular-webfont.woff HTTP/1.1" 200 85876
INFO 2025-07-27 20:43:06,423 basehttp 17144 19076 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-07-27 20:43:06,423 basehttp 17144 19076 "GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
INFO 2025-07-27 20:43:06,431 basehttp 17144 14572 "GET /static/admin/fonts/Roboto-Light-webfont.woff HTTP/1.1" 200 85692
INFO 2025-07-27 20:43:06,431 basehttp 17144 19076 "GET /static/admin/fonts/Roboto-Bold-webfont.woff HTTP/1.1" 200 86184
INFO 2025-07-27 20:43:06,431 basehttp 17144 14572 "GET /static/admin/fonts/Roboto-Light-webfont.woff HTTP/1.1" 200 85692
INFO 2025-07-27 20:43:06,431 basehttp 17144 19076 "GET /static/admin/fonts/Roboto-Bold-webfont.woff HTTP/1.1" 200 86184
INFO 2025-07-27 20:43:08,749 basehttp 17144 14572 "GET /admin/routes/userprofile/ HTTP/1.1" 200 46817
INFO 2025-07-27 20:43:08,749 basehttp 17144 14572 "GET /admin/routes/userprofile/ HTTP/1.1" 200 46817
INFO 2025-07-27 20:43:08,763 basehttp 17144 14572 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:43:08,763 basehttp 17144 14572 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:43:09,512 basehttp 17144 14572 "GET /admin/routes/usertag/ HTTP/1.1" 200 42030
INFO 2025-07-27 20:43:09,512 basehttp 17144 14572 "GET /admin/routes/usertag/ HTTP/1.1" 200 42030
INFO 2025-07-27 20:43:09,526 basehttp 17144 14572 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:43:09,526 basehttp 17144 14572 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:43:19,609 basehttp 17144 14572 "GET /admin/routes/routesearch/?route_tag__exact=%E5%95%86%E5%8A%A1&user__id__exact=3 HTTP/1.1" 200 48703
INFO 2025-07-27 20:43:19,609 basehttp 17144 14572 "GET /admin/routes/routesearch/?route_tag__exact=%E5%95%86%E5%8A%A1&user__id__exact=3 HTTP/1.1" 200 48703
INFO 2025-07-27 20:43:19,628 basehttp 17144 14572 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:43:19,628 basehttp 17144 14572 "GET /admin/jsi18n/ HTTP/1.1" 200 7732
INFO 2025-07-27 20:43:25,780 basehttp 17144 14572 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:43:25,780 basehttp 17144 14572 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:43:26,927 basehttp 17144 14572 "GET /api/route-history/ HTTP/1.1" 200 6339
INFO 2025-07-27 20:43:26,927 basehttp 17144 14572 "GET /api/route-history/ HTTP/1.1" 200 6339
INFO 2025-07-27 20:43:27,936 basehttp 17144 14572 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:43:27,936 basehttp 17144 14572 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:43:27,996 basehttp 17144 14572 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2650
INFO 2025-07-27 20:43:27,996 basehttp 17144 14572 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2650
INFO 2025-07-27 20:45:03,034 autoreload 19832 18488 Watching for file changes with StatReloader
INFO 2025-07-27 20:45:03,034 autoreload 19832 18488 Watching for file changes with StatReloader
INFO 2025-07-27 20:47:25,087 autoreload 15324 17712 Watching for file changes with StatReloader
INFO 2025-07-27 20:47:25,087 autoreload 15324 17712 Watching for file changes with StatReloader
INFO 2025-07-27 20:47:27,292 basehttp 15324 17196 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:47:27,292 basehttp 15324 17196 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:47:28,559 basehttp 15324 17196 "GET /api/route-history/ HTTP/1.1" 200 6339
INFO 2025-07-27 20:47:28,559 basehttp 15324 17196 "GET /api/route-history/ HTTP/1.1" 200 6339
INFO 2025-07-27 20:47:29,291 basehttp 15324 17196 "GET /logout/ HTTP/1.1" 302 0
INFO 2025-07-27 20:47:29,291 basehttp 15324 17196 "GET /logout/ HTTP/1.1" 302 0
INFO 2025-07-27 20:47:29,311 basehttp 15324 17196 "GET / HTTP/1.1" 200 97354
INFO 2025-07-27 20:47:29,311 basehttp 15324 17196 "GET / HTTP/1.1" 200 97354
INFO 2025-07-27 20:47:30,447 basehttp 15324 17196 "GET /api/route-history/ HTTP/1.1" 200 5019
INFO 2025-07-27 20:47:30,447 basehttp 15324 17196 "GET /api/route-history/ HTTP/1.1" 200 5019
INFO 2025-07-27 20:47:31,945 basehttp 15324 17196 "GET /login/ HTTP/1.1" 200 12628
INFO 2025-07-27 20:47:31,945 basehttp 15324 17196 "GET /login/ HTTP/1.1" 200 12628
INFO 2025-07-27 20:47:34,304 basehttp 15324 17196 "POST /login/ HTTP/1.1" 200 81
INFO 2025-07-27 20:47:34,304 basehttp 15324 17196 "POST /login/ HTTP/1.1" 200 81
INFO 2025-07-27 20:47:34,331 basehttp 15324 17196 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:47:34,331 basehttp 15324 17196 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:47:35,497 basehttp 15324 17196 "GET /api/route-history/ HTTP/1.1" 200 6339
INFO 2025-07-27 20:47:35,497 basehttp 15324 17196 "GET /api/route-history/ HTTP/1.1" 200 6339
INFO 2025-07-27 20:47:36,766 basehttp 15324 17196 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:47:36,766 basehttp 15324 17196 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:47:36,824 basehttp 15324 17196 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2650
INFO 2025-07-27 20:47:36,824 basehttp 15324 17196 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2650
INFO 2025-07-27 20:47:37,283 basehttp 15324 17196 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:47:37,283 basehttp 15324 17196 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:47:37,308 recommendations 15324 17196 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:47:37,381 basehttp 15324 17196 "GET /api/route-recommendations/?_=1753620457294 HTTP/1.1" 200 2241
INFO 2025-07-27 20:47:37,381 basehttp 15324 17196 "GET /api/route-recommendations/?_=1753620457294 HTTP/1.1" 200 2241
INFO 2025-07-27 20:47:38,260 ensemble 15324 17196 �յ��Ƽ�API�����û�: 1���㷨: item_cf������: 5
INFO 2025-07-27 20:47:38,262 ensemble 15324 17196 ��ʼ���Ƽ����������û�: 1���Ƽ�����: 5
INFO 2025-07-27 20:47:38,262 ensemble 15324 17196 Ϊ�û� 1 ��ȡ item_cf �㷨���Ƽ�
INFO 2025-07-27 20:47:38,262 collaborative_filtering 15324 17196 ��ʼ��������Ʒ��Эͬ�����Ƽ��㷨��top_n=5, min_similarity=0.001
INFO 2025-07-27 20:47:38,265 collaborative_filtering 15324 17196 �ҵ� 193 ����ͬ��·��
INFO 2025-07-27 20:47:40,354 collaborative_filtering 15324 17196 ������ 193 ��·�ߵ��û�����
INFO 2025-07-27 20:47:40,354 collaborative_filtering 15324 17196 ·��-�û������й��� 650 ������Ԫ��
INFO 2025-07-27 20:47:40,354 collaborative_filtering 15324 17196 �����а��� 11 ���û�
INFO 2025-07-27 20:47:40,366 collaborative_filtering 15324 17196 ����õ� 16938 ������·�ߣ�ƽ��ÿ��·���� 87.76 ������·��
INFO 2025-07-27 20:47:40,368 collaborative_filtering 15324 17196 �û� 1 �� 58 ����ʷ·��
INFO 2025-07-27 20:47:40,368 collaborative_filtering 15324 17196 ��ѡ�Ƽ�·������: 89
INFO 2025-07-27 20:47:40,368 collaborative_filtering 15324 17196 ѡȡ�� 5 ���Ƽ�·��
INFO 2025-07-27 20:47:40,369 ensemble 15324 17196 �ɹ���ȡ�� 5 ���Ƽ�
INFO 2025-07-27 20:47:40,369 ensemble 15324 17196 �㷨 item_cf ԭʼ�Ƽ�����: 5
INFO 2025-07-27 20:47:40,369 ensemble 15324 17196 ȥ�غ�ʣ�� 5 ���Ƽ�
INFO 2025-07-27 20:47:40,370 basehttp 15324 17196 "GET /api/advanced-recommendations/?algorithm=item_cf&_=1753620458253 HTTP/1.1" 200 2568
INFO 2025-07-27 20:47:40,370 basehttp 15324 17196 "GET /api/advanced-recommendations/?algorithm=item_cf&_=1753620458253 HTTP/1.1" 200 2568
INFO 2025-07-27 20:47:41,007 basehttp 15324 17196 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:47:41,007 basehttp 15324 17196 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:47:41,064 basehttp 15324 17196 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2650
INFO 2025-07-27 20:47:41,064 basehttp 15324 17196 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2650
INFO 2025-07-27 20:47:43,184 basehttp 15324 17196 "GET /api/search-routes-v2/?keyword=%E5%8C%97%E4%BA%AC&page=1&page_size=10 HTTP/1.1" 200 3120
INFO 2025-07-27 20:47:43,184 basehttp 15324 17196 "GET /api/search-routes-v2/?keyword=%E5%8C%97%E4%BA%AC&page=1&page_size=10 HTTP/1.1" 200 3120
INFO 2025-07-27 20:47:44,068 basehttp 15324 17196 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:47:44,068 basehttp 15324 17196 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:47:44,326 basehttp 15324 17196 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:47:44,327 basehttp 15324 6896 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:47:44,326 basehttp 15324 17196 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:47:44,327 basehttp 15324 6896 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:47:44,327 basehttp 15324 18800 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:47:44,327 basehttp 15324 18800 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:47:44,415 basehttp 15324 17196 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:47:44,415 basehttp 15324 17196 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:47:44,500 basehttp 15324 6896 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:47:44,500 basehttp 15324 6896 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:47:45,588 basehttp 15324 6896 "GET /api/analytics/heatmap_data/ HTTP/1.1" 200 122593
INFO 2025-07-27 20:47:45,588 basehttp 15324 6896 "GET /api/analytics/heatmap_data/ HTTP/1.1" 200 122593
INFO 2025-07-27 20:47:45,941 basehttp 15324 17196 "GET /api/analytics/route_density_data/ HTTP/1.1" 200 2287548
INFO 2025-07-27 20:47:45,941 basehttp 15324 17196 "GET /api/analytics/route_density_data/ HTTP/1.1" 200 2287548
INFO 2025-07-27 20:47:46,093 basehttp 15324 17196 "GET /api/analytics/daily_trend/ HTTP/1.1" 200 145
INFO 2025-07-27 20:47:46,093 basehttp 15324 17196 "GET /api/analytics/daily_trend/ HTTP/1.1" 200 145
INFO 2025-07-27 20:47:46,096 basehttp 15324 6896 "GET /api/analytics/hourly_distribution/ HTTP/1.1" 200 338
INFO 2025-07-27 20:47:46,096 basehttp 15324 18800 "GET /api/analytics/monthly_trend/ HTTP/1.1" 200 72
INFO 2025-07-27 20:47:46,096 basehttp 15324 6896 "GET /api/analytics/hourly_distribution/ HTTP/1.1" 200 338
INFO 2025-07-27 20:47:46,096 basehttp 15324 18800 "GET /api/analytics/monthly_trend/ HTTP/1.1" 200 72
INFO 2025-07-27 20:47:46,967 basehttp 15324 18800 "GET /api/analytics/mode_comparison/ HTTP/1.1" 200 455
INFO 2025-07-27 20:47:46,968 basehttp 15324 16080 "GET /api/analytics/tag_analysis/ HTTP/1.1" 200 1226
INFO 2025-07-27 20:47:46,967 basehttp 15324 18800 "GET /api/analytics/mode_comparison/ HTTP/1.1" 200 455
INFO 2025-07-27 20:47:46,968 basehttp 15324 16080 "GET /api/analytics/tag_analysis/ HTTP/1.1" 200 1226
INFO 2025-07-27 20:47:46,972 basehttp 15324 6896 "GET /api/analytics/distance_time_relation/ HTTP/1.1" 200 241596
INFO 2025-07-27 20:47:46,972 basehttp 15324 6896 "GET /api/analytics/distance_time_relation/ HTTP/1.1" 200 241596
INFO 2025-07-27 20:47:47,141 basehttp 15324 7128 "GET /api/analytics/periodic_pattern_analysis/ HTTP/1.1" 200 306
INFO 2025-07-27 20:47:47,141 basehttp 15324 7128 "GET /api/analytics/periodic_pattern_analysis/ HTTP/1.1" 200 306
INFO 2025-07-27 20:47:47,142 basehttp 15324 6896 "GET /api/analytics/time_space_heatmap/ HTTP/1.1" 200 1474
INFO 2025-07-27 20:47:47,142 basehttp 15324 6896 "GET /api/analytics/time_space_heatmap/ HTTP/1.1" 200 1474
INFO 2025-07-27 20:47:47,145 basehttp 15324 18800 "GET /api/analytics/region_time_density/ HTTP/1.1" 200 12544
INFO 2025-07-27 20:47:47,145 basehttp 15324 18800 "GET /api/analytics/region_time_density/ HTTP/1.1" 200 12544
INFO 2025-07-27 20:47:47,279 basehttp 15324 16080 "GET /api/analytics/time_modes_comparison/ HTTP/1.1" 200 1957
INFO 2025-07-27 20:47:47,279 basehttp 15324 16080 "GET /api/analytics/time_modes_comparison/ HTTP/1.1" 200 1957
INFO 2025-07-27 20:47:47,989 basehttp 15324 17196 "GET /api/analytics/user_comparison/ HTTP/1.1" 200 1634
INFO 2025-07-27 20:47:47,989 basehttp 15324 17196 "GET /api/analytics/user_comparison/ HTTP/1.1" 200 1634
INFO 2025-07-27 20:47:48,492 basehttp 15324 17196 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:47:48,492 basehttp 15324 17196 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:47:48,537 basehttp 15324 16080 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:47:48,537 basehttp 15324 16080 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:47:48,537 basehttp 15324 17196 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:47:48,537 basehttp 15324 17196 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:47:55,240 basehttp 15324 17196 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 104
INFO 2025-07-27 20:47:55,240 basehttp 15324 17196 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 104
INFO 2025-07-27 20:47:55,254 basehttp 15324 17196 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:47:55,254 basehttp 15324 17196 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:48:08,747 basehttp 15324 17196 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 104
INFO 2025-07-27 20:48:08,747 basehttp 15324 17196 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 104
INFO 2025-07-27 20:48:08,761 basehttp 15324 17196 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:48:08,761 basehttp 15324 17196 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:48:11,963 basehttp 15324 17196 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 104
INFO 2025-07-27 20:48:11,963 basehttp 15324 17196 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 104
INFO 2025-07-27 20:48:11,979 basehttp 15324 17196 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:48:11,979 basehttp 15324 17196 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:48:33,156 basehttp 15324 17196 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:48:33,156 basehttp 15324 17196 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:48:33,456 basehttp 15324 18800 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:48:33,456 basehttp 15324 18800 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:48:33,488 basehttp 15324 6896 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:48:33,488 basehttp 15324 6896 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:48:33,520 basehttp 15324 17196 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:48:33,551 basehttp 15324 16080 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:48:33,520 basehttp 15324 17196 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:48:33,551 basehttp 15324 16080 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:48:33,558 basehttp 15324 7128 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:48:33,558 basehttp 15324 7128 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:48:35,182 basehttp 15324 7128 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:48:35,182 basehttp 15324 7128 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:48:35,229 basehttp 15324 16080 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:48:35,229 basehttp 15324 16080 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:48:35,230 basehttp 15324 7128 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:48:35,230 basehttp 15324 7128 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:50:57,556 autoreload 15324 17712 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\views\__init__.py changed, reloading.
INFO 2025-07-27 20:50:57,556 autoreload 15324 17712 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\views\__init__.py changed, reloading.
INFO 2025-07-27 20:50:58,483 autoreload 15968 14564 Watching for file changes with StatReloader
INFO 2025-07-27 20:50:58,483 autoreload 15968 14564 Watching for file changes with StatReloader
INFO 2025-07-27 20:51:04,944 autoreload 15968 14564 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\recommenders\recommendations.py changed, reloading.
INFO 2025-07-27 20:51:04,944 autoreload 15968 14564 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\recommenders\recommendations.py changed, reloading.
INFO 2025-07-27 20:51:06,102 autoreload 16196 19520 Watching for file changes with StatReloader
INFO 2025-07-27 20:51:06,102 autoreload 16196 19520 Watching for file changes with StatReloader
INFO 2025-07-27 20:51:32,924 basehttp 16196 13476 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:51:32,924 basehttp 16196 13476 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:51:32,987 basehttp 16196 14868 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:51:32,987 basehttp 16196 13476 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:51:32,987 basehttp 16196 14868 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:51:32,987 basehttp 16196 13476 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:51:44,405 basehttp 16196 13476 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 104
INFO 2025-07-27 20:51:44,405 basehttp 16196 13476 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 104
INFO 2025-07-27 20:51:44,420 basehttp 16196 13476 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:51:44,420 basehttp 16196 13476 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:52:44,454 autoreload 16196 19520 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\views\tags.py changed, reloading.
INFO 2025-07-27 20:52:44,454 autoreload 16196 19520 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\views\tags.py changed, reloading.
INFO 2025-07-27 20:52:45,124 autoreload 13084 6752 Watching for file changes with StatReloader
INFO 2025-07-27 20:52:45,124 autoreload 13084 6752 Watching for file changes with StatReloader
INFO 2025-07-27 20:53:00,378 autoreload 13084 6752 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\views\tags.py changed, reloading.
INFO 2025-07-27 20:53:00,378 autoreload 13084 6752 E:\bf\����\����Python·�߹滮���ݷ����Ƽ�ϵͳ\routes\views\tags.py changed, reloading.
INFO 2025-07-27 20:53:00,992 autoreload 17252 19076 Watching for file changes with StatReloader
INFO 2025-07-27 20:53:00,992 autoreload 17252 19076 Watching for file changes with StatReloader
INFO 2025-07-27 20:53:11,012 autoreload 13036 19836 Watching for file changes with StatReloader
INFO 2025-07-27 20:53:11,012 autoreload 13036 19836 Watching for file changes with StatReloader
INFO 2025-07-27 20:54:13,819 basehttp 17252 14668 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:54:13,819 basehttp 17252 14668 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:54:13,880 basehttp 17252 4244 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:54:13,880 basehttp 17252 4244 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 306
INFO 2025-07-27 20:54:13,880 basehttp 17252 14668 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:13,880 basehttp 17252 14668 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:20,654 basehttp 17252 14668 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 105
INFO 2025-07-27 20:54:20,654 basehttp 17252 14668 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 105
INFO 2025-07-27 20:54:20,668 basehttp 17252 14668 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:20,668 basehttp 17252 14668 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:22,450 basehttp 17252 14668 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:54:22,450 basehttp 17252 14668 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:54:22,490 basehttp 17252 4244 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:22,490 basehttp 17252 14668 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 263
INFO 2025-07-27 20:54:22,490 basehttp 17252 4244 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:22,490 basehttp 17252 14668 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 263
INFO 2025-07-27 20:54:27,054 basehttp 17252 14668 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 105
INFO 2025-07-27 20:54:27,054 basehttp 17252 14668 "POST /api/tags/apply_batch_tag/ HTTP/1.1" 200 105
INFO 2025-07-27 20:54:27,068 basehttp 17252 14668 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:27,068 basehttp 17252 14668 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:30,081 basehttp 17252 14668 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:54:30,081 basehttp 17252 14668 "GET /tags/ HTTP/1.1" 200 28575
INFO 2025-07-27 20:54:30,126 basehttp 17252 4244 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:30,126 basehttp 17252 14668 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 263
INFO 2025-07-27 20:54:30,126 basehttp 17252 4244 "GET /api/tags/get_tag_stats/ HTTP/1.1" 200 262
INFO 2025-07-27 20:54:30,126 basehttp 17252 14668 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 263
INFO 2025-07-27 20:54:34,738 basehttp 17252 14668 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:54:34,738 basehttp 17252 14668 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:54:34,802 basehttp 17252 14668 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2758
INFO 2025-07-27 20:54:34,802 basehttp 17252 14668 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2758
INFO 2025-07-27 20:54:41,941 basehttp 17252 14668 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:54:41,941 basehttp 17252 14668 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:54:41,971 recommendations 17252 14668 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:54:42,036 basehttp 17252 14668 "GET /api/route-recommendations/?_=1753620881951 HTTP/1.1" 200 2241
INFO 2025-07-27 20:54:42,036 basehttp 17252 14668 "GET /api/route-recommendations/?_=1753620881951 HTTP/1.1" 200 2241
INFO 2025-07-27 20:54:42,934 ensemble 17252 14668 �յ��Ƽ�API�����û�: 1���㷨: item_cf������: 5
INFO 2025-07-27 20:54:42,934 ensemble 17252 14668 ��ʼ���Ƽ����������û�: 1���Ƽ�����: 5
INFO 2025-07-27 20:54:42,934 ensemble 17252 14668 Ϊ�û� 1 ��ȡ item_cf �㷨���Ƽ�
INFO 2025-07-27 20:54:42,934 collaborative_filtering 17252 14668 ��ʼ��������Ʒ��Эͬ�����Ƽ��㷨��top_n=5, min_similarity=0.001
INFO 2025-07-27 20:54:42,936 collaborative_filtering 17252 14668 �ҵ� 193 ����ͬ��·��
INFO 2025-07-27 20:54:44,987 collaborative_filtering 17252 14668 ������ 193 ��·�ߵ��û�����
INFO 2025-07-27 20:54:44,987 collaborative_filtering 17252 14668 ·��-�û������й��� 650 ������Ԫ��
INFO 2025-07-27 20:54:44,988 collaborative_filtering 17252 14668 �����а��� 11 ���û�
INFO 2025-07-27 20:54:44,997 collaborative_filtering 17252 14668 ����õ� 16938 ������·�ߣ�ƽ��ÿ��·���� 87.76 ������·��
INFO 2025-07-27 20:54:44,999 collaborative_filtering 17252 14668 �û� 1 �� 58 ����ʷ·��
INFO 2025-07-27 20:54:44,999 collaborative_filtering 17252 14668 ��ѡ�Ƽ�·������: 89
INFO 2025-07-27 20:54:44,999 collaborative_filtering 17252 14668 ѡȡ�� 5 ���Ƽ�·��
INFO 2025-07-27 20:54:44,999 ensemble 17252 14668 �ɹ���ȡ�� 5 ���Ƽ�
INFO 2025-07-27 20:54:45,000 ensemble 17252 14668 �㷨 item_cf ԭʼ�Ƽ�����: 5
INFO 2025-07-27 20:54:45,000 ensemble 17252 14668 ȥ�غ�ʣ�� 5 ���Ƽ�
INFO 2025-07-27 20:54:45,000 basehttp 17252 14668 "GET /api/advanced-recommendations/?algorithm=item_cf&_=1753620882927 HTTP/1.1" 200 2568
INFO 2025-07-27 20:54:45,000 basehttp 17252 14668 "GET /api/advanced-recommendations/?algorithm=item_cf&_=1753620882927 HTTP/1.1" 200 2568
INFO 2025-07-27 20:54:45,546 ensemble 17252 14668 �յ��Ƽ�API�����û�: 1���㷨: matrix_factorization������: 5
INFO 2025-07-27 20:54:45,546 ensemble 17252 14668 ��ʼ���Ƽ����������û�: 1���Ƽ�����: 5
INFO 2025-07-27 20:54:45,546 ensemble 17252 14668 Ϊ�û� 1 ��ȡ matrix_factorization �㷨���Ƽ�
INFO 2025-07-27 20:54:45,578 ensemble 17252 14668 �ɹ���ȡ�� 5 ���Ƽ�
INFO 2025-07-27 20:54:45,579 ensemble 17252 14668 �㷨 matrix_factorization ԭʼ�Ƽ�����: 5
INFO 2025-07-27 20:54:45,579 ensemble 17252 14668 ȥ�غ�ʣ�� 5 ���Ƽ�
INFO 2025-07-27 20:54:45,580 basehttp 17252 14668 "GET /api/advanced-recommendations/?algorithm=matrix_factorization&_=1753620885539 HTTP/1.1" 200 2445
INFO 2025-07-27 20:54:45,580 basehttp 17252 14668 "GET /api/advanced-recommendations/?algorithm=matrix_factorization&_=1753620885539 HTTP/1.1" 200 2445
INFO 2025-07-27 20:54:47,739 ensemble 17252 14668 ��ʼ���Ƽ����������û�: 1���Ƽ�����: 5
INFO 2025-07-27 20:54:47,739 ensemble 17252 14668 Ϊ�û� 1 ��ȡ location_based �㷨���Ƽ�
INFO 2025-07-27 20:54:47,805 basehttp 17252 14668 "GET /api/advanced-recommendations/?algorithm=location_based&_=1753620887731 HTTP/1.1" 200 2630
INFO 2025-07-27 20:54:47,805 basehttp 17252 14668 "GET /api/advanced-recommendations/?algorithm=location_based&_=1753620887731 HTTP/1.1" 200 2630
INFO 2025-07-27 20:54:49,703 basehttp 17252 14668 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:54:49,703 basehttp 17252 14668 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:54:50,855 basehttp 17252 14668 "GET /api/route-history/ HTTP/1.1" 200 6447
INFO 2025-07-27 20:54:50,855 basehttp 17252 14668 "GET /api/route-history/ HTTP/1.1" 200 6447
INFO 2025-07-27 20:55:29,402 basehttp 17252 14668 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:55:29,402 basehttp 17252 14668 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:55:29,459 basehttp 17252 14668 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2758
INFO 2025-07-27 20:55:29,459 basehttp 17252 14668 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2758
INFO 2025-07-27 20:55:30,198 basehttp 17252 14668 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:55:30,198 basehttp 17252 14668 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:55:30,863 basehttp 17252 14668 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:55:30,863 basehttp 17252 14668 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:55:30,920 basehttp 17252 14668 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2758
INFO 2025-07-27 20:55:30,920 basehttp 17252 14668 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2758
INFO 2025-07-27 20:55:31,452 basehttp 17252 14668 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:55:31,452 basehttp 17252 14668 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:55:31,488 recommendations 17252 14668 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:55:31,552 basehttp 17252 14668 "GET /api/route-recommendations/?_=1753620931463 HTTP/1.1" 200 2284
INFO 2025-07-27 20:55:31,552 basehttp 17252 14668 "GET /api/route-recommendations/?_=1753620931463 HTTP/1.1" 200 2284
INFO 2025-07-27 20:55:32,116 basehttp 17252 14668 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:55:32,116 basehttp 17252 14668 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:55:32,336 basehttp 17252 4244 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:55:32,336 basehttp 17252 4244 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:55:32,337 basehttp 17252 14668 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:55:32,337 basehttp 17252 14668 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:55:32,387 basehttp 17252 4244 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:55:32,387 basehttp 17252 14668 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:55:32,387 basehttp 17252 4244 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:55:32,387 basehttp 17252 14668 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:55:32,534 basehttp 17252 4244 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:55:32,534 basehttp 17252 4244 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:55:33,216 basehttp 17252 4244 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:55:33,216 basehttp 17252 4244 "GET /search/ HTTP/1.1" 200 25407
INFO 2025-07-27 20:55:33,275 basehttp 17252 4244 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2758
INFO 2025-07-27 20:55:33,275 basehttp 17252 4244 "GET /api/search-routes-v2/?page=1&page_size=10 HTTP/1.1" 200 2758
INFO 2025-07-27 20:55:33,746 basehttp 17252 4244 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:55:33,746 basehttp 17252 4244 "GET /recommendations/ HTTP/1.1" 200 26409
INFO 2025-07-27 20:55:33,782 recommendations 17252 4244 �û� 1 ����·���Ƽ�
INFO 2025-07-27 20:55:33,848 basehttp 17252 4244 "GET /api/route-recommendations/?_=1753620933757 HTTP/1.1" 200 2284
INFO 2025-07-27 20:55:33,848 basehttp 17252 4244 "GET /api/route-recommendations/?_=1753620933757 HTTP/1.1" 200 2284
INFO 2025-07-27 20:55:34,884 ensemble 17252 4244 �յ��Ƽ�API�����û�: 1���㷨: item_cf������: 5
INFO 2025-07-27 20:55:34,884 ensemble 17252 4244 ��ʼ���Ƽ����������û�: 1���Ƽ�����: 5
INFO 2025-07-27 20:55:34,884 ensemble 17252 4244 Ϊ�û� 1 ��ȡ item_cf �㷨���Ƽ�
INFO 2025-07-27 20:55:34,884 collaborative_filtering 17252 4244 ��ʼ��������Ʒ��Эͬ�����Ƽ��㷨��top_n=5, min_similarity=0.001
INFO 2025-07-27 20:55:34,886 collaborative_filtering 17252 4244 �ҵ� 193 ����ͬ��·��
INFO 2025-07-27 20:55:36,900 collaborative_filtering 17252 4244 ������ 193 ��·�ߵ��û�����
INFO 2025-07-27 20:55:36,901 collaborative_filtering 17252 4244 ·��-�û������й��� 650 ������Ԫ��
INFO 2025-07-27 20:55:36,901 collaborative_filtering 17252 4244 �����а��� 11 ���û�
INFO 2025-07-27 20:55:36,909 collaborative_filtering 17252 4244 ����õ� 16938 ������·�ߣ�ƽ��ÿ��·���� 87.76 ������·��
INFO 2025-07-27 20:55:36,911 collaborative_filtering 17252 4244 �û� 1 �� 58 ����ʷ·��
INFO 2025-07-27 20:55:36,912 collaborative_filtering 17252 4244 ��ѡ�Ƽ�·������: 89
INFO 2025-07-27 20:55:36,912 collaborative_filtering 17252 4244 ѡȡ�� 5 ���Ƽ�·��
INFO 2025-07-27 20:55:36,912 ensemble 17252 4244 �ɹ���ȡ�� 5 ���Ƽ�
INFO 2025-07-27 20:55:36,912 ensemble 17252 4244 �㷨 item_cf ԭʼ�Ƽ�����: 5
INFO 2025-07-27 20:55:36,913 ensemble 17252 4244 ȥ�غ�ʣ�� 5 ���Ƽ�
INFO 2025-07-27 20:55:36,913 basehttp 17252 4244 "GET /api/advanced-recommendations/?algorithm=item_cf&_=1753620934876 HTTP/1.1" 200 2568
INFO 2025-07-27 20:55:36,913 basehttp 17252 4244 "GET /api/advanced-recommendations/?algorithm=item_cf&_=1753620934876 HTTP/1.1" 200 2568
INFO 2025-07-27 20:55:37,945 ensemble 17252 4244 �յ��Ƽ�API�����û�: 1���㷨: matrix_factorization������: 5
INFO 2025-07-27 20:55:37,945 ensemble 17252 4244 ��ʼ���Ƽ����������û�: 1���Ƽ�����: 5
INFO 2025-07-27 20:55:37,945 ensemble 17252 4244 Ϊ�û� 1 ��ȡ matrix_factorization �㷨���Ƽ�
INFO 2025-07-27 20:55:37,952 ensemble 17252 4244 �ɹ���ȡ�� 5 ���Ƽ�
INFO 2025-07-27 20:55:37,953 ensemble 17252 4244 �㷨 matrix_factorization ԭʼ�Ƽ�����: 5
INFO 2025-07-27 20:55:37,953 ensemble 17252 4244 ȥ�غ�ʣ�� 5 ���Ƽ�
INFO 2025-07-27 20:55:37,954 basehttp 17252 4244 "GET /api/advanced-recommendations/?algorithm=matrix_factorization&_=1753620937938 HTTP/1.1" 200 2443
INFO 2025-07-27 20:55:37,954 basehttp 17252 4244 "GET /api/advanced-recommendations/?algorithm=matrix_factorization&_=1753620937938 HTTP/1.1" 200 2443
INFO 2025-07-27 20:55:40,032 basehttp 17252 4244 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:55:40,032 basehttp 17252 4244 "GET /analytics/ HTTP/1.1" 200 31078
INFO 2025-07-27 20:55:40,201 basehttp 17252 1204 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:55:40,201 basehttp 17252 1204 "GET /api/analytics/popular_destinations/ HTTP/1.1" 200 811
INFO 2025-07-27 20:55:40,217 basehttp 17252 14668 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:55:40,218 basehttp 17252 4244 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:55:40,217 basehttp 17252 14668 "GET /api/analytics/mode_distribution/ HTTP/1.1" 200 191
INFO 2025-07-27 20:55:40,218 basehttp 17252 4244 "GET /api/analytics/overview/ HTTP/1.1" 200 131
INFO 2025-07-27 20:55:40,218 basehttp 17252 16080 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:55:40,218 basehttp 17252 16080 "GET /api/analytics/distance_distribution/ HTTP/1.1" 200 199
INFO 2025-07-27 20:55:40,357 basehttp 17252 7128 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:55:40,357 basehttp 17252 7128 "GET /api/analytics/popular_routes/ HTTP/1.1" 200 1953
INFO 2025-07-27 20:55:41,272 basehttp 17252 14668 "GET /api/analytics/periodic_pattern_analysis/ HTTP/1.1" 200 306
INFO 2025-07-27 20:55:41,273 basehttp 17252 4244 "GET /api/analytics/region_time_density/ HTTP/1.1" 200 12544
INFO 2025-07-27 20:55:41,272 basehttp 17252 14668 "GET /api/analytics/periodic_pattern_analysis/ HTTP/1.1" 200 306
INFO 2025-07-27 20:55:41,273 basehttp 17252 4244 "GET /api/analytics/region_time_density/ HTTP/1.1" 200 12544
INFO 2025-07-27 20:55:41,288 basehttp 17252 16080 "GET /api/analytics/time_space_heatmap/ HTTP/1.1" 200 1474
INFO 2025-07-27 20:55:41,288 basehttp 17252 16080 "GET /api/analytics/time_space_heatmap/ HTTP/1.1" 200 1474
INFO 2025-07-27 20:55:41,406 basehttp 17252 7128 "GET /api/analytics/time_modes_comparison/ HTTP/1.1" 200 1957
INFO 2025-07-27 20:55:41,406 basehttp 17252 7128 "GET /api/analytics/time_modes_comparison/ HTTP/1.1" 200 1957
INFO 2025-07-27 20:55:42,282 basehttp 17252 7128 "GET /api/analytics/mode_comparison/ HTTP/1.1" 200 455
INFO 2025-07-27 20:55:42,282 basehttp 17252 7128 "GET /api/analytics/mode_comparison/ HTTP/1.1" 200 455
INFO 2025-07-27 20:55:42,285 basehttp 17252 16080 "GET /api/analytics/distance_time_relation/ HTTP/1.1" 200 241596
INFO 2025-07-27 20:55:42,285 basehttp 17252 16080 "GET /api/analytics/distance_time_relation/ HTTP/1.1" 200 241596
INFO 2025-07-27 20:55:42,288 basehttp 17252 4244 "GET /api/analytics/tag_analysis/ HTTP/1.1" 200 1227
INFO 2025-07-27 20:55:42,288 basehttp 17252 4244 "GET /api/analytics/tag_analysis/ HTTP/1.1" 200 1227
INFO 2025-07-27 20:55:42,994 basehttp 17252 4244 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:55:42,994 basehttp 17252 4244 "GET / HTTP/1.1" 200 100743
INFO 2025-07-27 20:55:43,049 basehttp 17252 14668 "GET /api/analytics/user_comparison/ HTTP/1.1" 200 1634
INFO 2025-07-27 20:55:43,049 basehttp 17252 14668 "GET /api/analytics/user_comparison/ HTTP/1.1" 200 1634
INFO 2025-07-27 20:55:44,118 basehttp 17252 14668 "GET /api/route-history/ HTTP/1.1" 200 6447
INFO 2025-07-27 20:55:44,118 basehttp 17252 14668 "GET /api/route-history/ HTTP/1.1" 200 6447
INFO 2025-07-27 20:55:46,406 basehttp 17252 14668 "GET /api/route-history/ HTTP/1.1" 200 6447
INFO 2025-07-27 20:55:46,406 basehttp 17252 14668 "GET /api/route-history/ HTTP/1.1" 200 6447
INFO 2025-07-27 20:55:46,425 basehttp 17252 14668 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 263
INFO 2025-07-27 20:55:46,425 basehttp 17252 14668 "GET /api/tags/get_user_tags/ HTTP/1.1" 200 263
