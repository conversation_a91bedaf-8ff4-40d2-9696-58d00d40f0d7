from django.urls import path
from . import views
from .recommenders.recommendations import route_recommendations, view_logs
from .recommenders.ensemble import get_recommendations_api
from .views import analytics

app_name = 'routes'

urlpatterns = [
    path('', views.index, name='index'),  # 首页路由
    
    # 用户认证路由
    path('login/', views.login_view, name='login'),
    path('register/', views.register_view, name='register'),
    path('logout/', views.logout_view, name='logout'),
    path('profile/', views.profile_view, name='profile'),
    path('search/', views.search_view, name='search'),
    
    # 路线推荐页面
    path('recommendations/', views.recommendations_view, name='recommendations'),
    
    # 个人地址管理
    path('api/add-favorite-address/', views.add_favorite_address, name='add_favorite_address'),
    path('api/delete-favorite-address/', views.delete_favorite_address, name='delete_favorite_address'),
    
    # 路线收藏和标签
    path('api/toggle-favorite/<int:record_id>/', views.toggle_favorite, name='toggle_favorite'),
    path('api/update-route-tag/<int:record_id>/', views.update_route_tag, name='update_route_tag'),
    
    # 路线规划API
    path('api/route-planning/', views.route_planning, name='route_planning'),  # 路线规划API
    path('api/route-history/', views.route_history, name='route_history'),  # 历史记录API
    path('api/route-detail/<int:record_id>/', views.route_detail, name='route_detail'),  # 路线详情API
    path('api/route-history-all/', views.route_history_all, name='route_history_all'),  # 获取所有历史路线数据（用于热力图）

    # 添加路线分析页面和API端点
    path('analytics/', views.analytics_view, name='analytics'),
    path('api/analytics/overview/', analytics.analytics_overview, name='api_analytics_overview'),
    path('api/analytics/mode_distribution/', analytics.analytics_mode_distribution, name='api_analytics_mode_distribution'),
    path('api/analytics/distance_distribution/', analytics.analytics_distance_distribution, name='api_analytics_distance_distribution'),
    path('api/analytics/popular_destinations/', analytics.analytics_popular_destinations, name='api_analytics_popular_destinations'),
    path('api/analytics/popular_routes/', analytics.analytics_popular_routes, name='api_analytics_popular_routes'),
    
    # 时间分布相关API
    path('api/analytics/daily_trend/', analytics.analytics_daily_trend, name='api_analytics_daily_trend'),
    path('api/analytics/hourly_distribution/', analytics.analytics_hourly_distribution, name='api_analytics_hourly_distribution'),
    path('api/analytics/monthly_trend/', analytics.analytics_monthly_trend, name='api_analytics_monthly_trend'),
    
    # 对比分析相关API
    path('api/analytics/mode_comparison/', analytics.analytics_mode_comparison, name='api_analytics_mode_comparison'),
    path('api/analytics/distance_time_relation/', analytics.analytics_distance_time_relation, name='api_analytics_distance_time_relation'),
    path('api/analytics/tag_analysis/', analytics.analytics_tag_analysis, name='api_analytics_tag_analysis'),
    path('api/analytics/user_comparison/', analytics.analytics_user_comparison, name='api_analytics_user_comparison'),
    
    # 空间分布相关API
    path('api/analytics/heatmap_data/', analytics.analytics_heatmap_data, name='api_analytics_heatmap_data'),
    path('api/analytics/route_density_data/', analytics.analytics_route_density_data, name='api_analytics_route_density_data'),
    
    # 添加路线搜索API
    path('api/search-routes/', views.search_routes, name='search_routes'),
    path('api/search-routes-v2/', views.search_routes_v2, name='search_routes_v2'),
    
    # 标签管理相关URL
    path('tags/', views.tags_view, name='tags_view'),
    path('api/tags/get_user_tags/', views.get_user_tags, name='get_user_tags'),
    path('api/tags/save_user_tag/', views.save_user_tag, name='save_user_tag'),
    path('api/tags/delete_user_tag/<int:tag_id>/', views.delete_user_tag, name='delete_user_tag'),
    path('api/tags/get_tag_stats/', views.get_tag_stats, name='get_tag_stats'),
    path('api/tags/apply_batch_tag/', views.apply_batch_tag, name='apply_batch_tag'),
    
    # 路线推荐API - 使用新的recommendations.py中的视图函数
    path('api/route-recommendations/', route_recommendations, name='route_recommendations'),
    
    # 新增：高级推荐API - 使用不同的推荐算法
    path('api/advanced-recommendations/', get_recommendations_api, name='advanced_recommendations'),
    
    # 新增的时间空间对比分析API端点
    path('api/analytics/time_space_heatmap/', analytics.time_space_heatmap, name='api_analytics_time_space_heatmap'),
    path('api/analytics/time_modes_comparison/', analytics.time_modes_comparison, name='api_analytics_time_modes_comparison'),
    path('api/analytics/region_time_density/', analytics.region_time_density, name='api_analytics_region_time_density'),
    path('api/analytics/periodic_pattern_analysis/', analytics.periodic_pattern_analysis, name='api_analytics_periodic_pattern_analysis'),
    
    # 新增：查看系统日志
    path('system-logs/', view_logs, name='view_logs'),
]