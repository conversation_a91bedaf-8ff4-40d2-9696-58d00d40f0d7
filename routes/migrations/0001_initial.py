# Generated by Django 3.0.10 on 2025-07-27 20:40

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='手机号')),
                ('default_city', models.CharField(blank=True, max_length=50, null=True, verbose_name='默认城市')),
                ('common_addresses', models.TextField(blank=True, null=True, verbose_name='常用地址')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '用户资料',
                'verbose_name_plural': '用户资料',
            },
        ),
        migrations.CreateModel(
            name='RouteSearch',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('origin', models.CharField(max_length=255, verbose_name='起点坐标')),
                ('destination', models.CharField(max_length=255, verbose_name='终点坐标')),
                ('origin_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='起点名称')),
                ('destination_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='终点名称')),
                ('distance', models.IntegerField(blank=True, null=True, verbose_name='距离(米)')),
                ('duration', models.IntegerField(blank=True, null=True, verbose_name='时间(秒)')),
                ('_route_data', models.TextField(blank=True, db_column='route_data', null=True, verbose_name='路线数据JSON')),
                ('created_time', models.DateTimeField(auto_now_add=True, db_index=True, verbose_name='创建时间')),
                ('is_favorite', models.BooleanField(default=False, verbose_name='是否收藏')),
                ('route_tag', models.CharField(blank=True, default='', max_length=50, verbose_name='路线标签')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='route_searches', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '路线搜索',
                'verbose_name_plural': '路线搜索',
                'ordering': ['-created_time'],
            },
        ),
        migrations.CreateModel(
            name='UserTag',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='标签名称')),
                ('created_time', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tags', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户标签',
                'verbose_name_plural': '用户标签',
                'ordering': ['name'],
                'unique_together': {('user', 'name')},
            },
        ),
    ]
