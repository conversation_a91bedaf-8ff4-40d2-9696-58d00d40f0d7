"""
推荐系统模块初始化文件
导入所有推荐算法，提供统一的接口
"""

# 导入各种推荐算法，使用try-except处理可能的导入错误
try:
    from .collaborative_filtering import get_item_cf_recommendations
except ImportError:
    # 提供备用函数
    def get_item_cf_recommendations(user, top_n=5):
        return []

try:
    from .matrix_factorization import get_mf_recommendations
except ImportError:
    # 提供备用函数
    def get_mf_recommendations(user, top_n=5):
        return []

try:
    from .location_based import get_location_recommendations
except ImportError:
    # 提供备用函数
    def get_location_recommendations(user, top_n=5):
        return []

# 导入原有的基于内容的推荐功能
try:
    from ..recommenders.recommendations import route_recommendations as content_based_recommendations
except ImportError:
    # 如果导入失败，提供一个空函数
    def content_based_recommendations(request):
        from django.http import JsonResponse
        return JsonResponse({
            'status': 'error',
            'message': '推荐系统模块加载失败'
        }) 