"""
个性化路线推荐系统相关视图
基于用户历史路线数据，推荐可能感兴趣的新路线或目的地
"""
import traceback
import logging
import os
from django.http import JsonResponse, HttpResponse
from django.db.models import Count
from django.contrib.auth.decorators import login_required
from django.db.utils import NotSupportedError
from django.conf import settings

from ..models import RouteSearch
from ..recommenders.location_based import get_location_recommendations

# 获取日志记录器
logger = logging.getLogger(__name__)

# 添加一个视图函数，用于显示日志文件内容
@login_required
def view_logs(request):
    """查看系统日志文件内容"""
    try:
        # 检查用户是否为管理员
        if not request.user.is_staff:
            return HttpResponse("您没有权限查看日志文件", status=403)

        # 尝试获取日志文件路径
        log_file = getattr(settings, 'LOGFILE_PATH', None)
        if not log_file:
            # 尝试查找常见的日志文件位置
            possible_log_paths = [
                os.path.join(settings.BASE_DIR, 'logs', 'debug.log'),
                os.path.join(settings.BASE_DIR, 'debug.log'),
                '/var/log/django/debug.log'
            ]

            for path in possible_log_paths:
                if os.path.exists(path):
                    log_file = path
                    break

        if not log_file or not os.path.exists(log_file):
            return HttpResponse("未找到日志文件", status=404)

        # 读取日志文件内容
        with open(log_file, 'r', encoding='utf-8') as f:
            # 读取最后1000行
            lines = f.readlines()[-1000:]
            content = ''.join(lines)

        # 过滤只显示推荐相关的日志
        if request.GET.get('filter') == 'recommendations':
            filtered_lines = [line for line in lines if 'recommend' in line.lower()]
            content = ''.join(filtered_lines)

        # 返回HTML格式的日志内容
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>系统日志</title>
            <style>
                body {{ font-family: monospace; background: #f5f5f5; padding: 20px; }}
                h1 {{ color: #333; }}
                .log-container {{ 
                    background: #fff; 
                    border: 1px solid #ddd; 
                    padding: 15px; 
                    border-radius: 5px;
                    white-space: pre-wrap;
                    word-wrap: break-word;
                    max-height: 80vh;
                    overflow-y: auto;
                }}
                .filter {{ margin-bottom: 20px; }}
                .error {{ color: red; }}
                .warning {{ color: orange; }}
                .info {{ color: blue; }}
                .debug {{ color: gray; }}
            </style>
        </head>
        <body>
            <h1>系统日志</h1>
            <div class="filter">
                <a href="?filter=recommendations">只显示推荐相关</a> | 
                <a href=".">显示全部</a>
            </div>
            <div class="log-container">
                {content.replace('ERROR', '<span class="error">ERROR</span>')
                       .replace('WARNING', '<span class="warning">WARNING</span>')
                       .replace('INFO', '<span class="info">INFO</span>')
                       .replace('DEBUG', '<span class="debug">DEBUG</span>')}
            </div>
        </body>
        </html>
        """

        return HttpResponse(html_content)
    except Exception as e:
        return HttpResponse(f"查看日志时出错: {str(e)}", status=500)


def route_recommendations(request):
    """
    获取个性化路线推荐API
    基于用户历史路线数据和偏好，推荐可能感兴趣的新路线
    """
    try:
        # 只对登录用户提供推荐
        if not request.user.is_authenticated:
            logger.warning("未登录用户尝试获取推荐")
            return JsonResponse({
                'status': 'error',
                'message': '请登录以获取个性化推荐'
            })

        logger.info(f"用户 {request.user.username} 请求路线推荐")

        # 首先尝试基于地理位置的推荐
        try:
            # 调用地理位置推荐函数
            location_recommendations = get_location_recommendations(request.user, top_n=5)

            # 检查是否成功获取到地理位置推荐
            if location_recommendations and len(location_recommendations) > 0:
                # 移除重复推荐
                unique_recommendations = []
                seen_routes = set()

                for rec in location_recommendations:
                    # 排除起点和终点相同的路线
                    if rec['origin'] == rec['destination'] or rec['origin_name'] == rec['destination_name']:
                        continue

                    route_key = f"{rec['origin']}_{rec['destination']}"
                    if route_key not in seen_routes:
                        seen_routes.add(route_key)
                        unique_recommendations.append(rec)

                # 如果地理位置推荐有结果，直接返回
                if len(unique_recommendations) > 0:
                    return JsonResponse({
                        'status': 'success',
                        'data': unique_recommendations,
                        'recommendation_type': 'location_based'
                    })

        except Exception as e:
            # 记录地理位置推荐失败的错误，但不中断流程
            pass

        logger.info("回退到基于内容的推荐")
        # 如果地理位置推荐失败或没有结果，回退到基于内容的推荐
        # 获取用户历史路线数据
        user_routes = RouteSearch.objects.filter(user=request.user).order_by('-created_time')
        logger.info(f"用户历史路线数量: {user_routes.count()}")

        # 如果用户没有足够的历史记录，返回热门路线
        if user_routes.count() < 3:
            logger.info("用户历史记录不足，使用热门路线推荐")
            # 获取系统热门路线作为推荐
            popular_routes = RouteSearch.objects.values('origin', 'destination', 'origin_name', 'destination_name') \
                .annotate(route_count=Count('id')) \
                .order_by('-route_count')[:5]

            logger.info(f"获取到 {popular_routes.count()} 条热门路线")

            recommendations = []
            for route in popular_routes:
                recommendations.append({
                    'type': '热门路线',
                    'origin': route['origin'],
                    'destination': route['destination'],
                    'origin_name': route['origin_name'],
                    'destination_name': route['destination_name'],
                    'reason': '基于系统热门路线推荐',
                    'confidence': 'medium'
                })

            # 移除重复推荐
            unique_recommendations = []
            seen_routes = set()

            for rec in recommendations:
                route_key = f"{rec['origin']}_{rec['destination']}"
                if route_key not in seen_routes:
                    seen_routes.add(route_key)
                    unique_recommendations.append(rec)

            logger.info(f"返回 {len(unique_recommendations)} 条热门路线推荐")
            return JsonResponse({
                'status': 'success',
                'data': unique_recommendations,
                'note': '根据系统热门路线为您推荐',
                'recommendation_type': 'content_based'
            })

        logger.info("开始分析用户偏好")
        # 分析用户的偏好模式
        # 1. 时间段偏好
        hour_preference = {}
        for route in user_routes:
            if route.created_time:
                hour = route.created_time.hour
                if hour not in hour_preference:
                    hour_preference[hour] = 0
                hour_preference[hour] += 1

        # 找出最常用的时间段
        favorite_hours = sorted(hour_preference.items(), key=lambda x: x[1], reverse=True)[:2]
        favorite_hours = [hour for hour, _ in favorite_hours]
        logger.info(f"用户常用时间段: {favorite_hours}")

        # 2. 距离偏好
        distance_sum = 0
        distance_count = 0
        for route in user_routes:
            if route.distance:
                distance_sum += route.distance
                distance_count += 1

        avg_distance = round(distance_sum / distance_count) if distance_count > 0 else 0
        distance_lower = max(0, avg_distance - 2000)  # 平均距离减2公里
        distance_upper = avg_distance + 2000  # 平均距离加2公里
        logger.info(f"用户平均出行距离: {avg_distance}米, 范围: {distance_lower}-{distance_upper}米")

        # 3. 出行方式偏好
        mode_preference = {}
        for route in user_routes:
            route_data = route.route_data
            if route_data and isinstance(route_data, dict) and 'mode' in route_data:
                mode = route_data['mode']
                if mode not in mode_preference:
                    mode_preference[mode] = 0
                mode_preference[mode] += 1

        # 找出最常用的出行方式
        favorite_mode = None
        if mode_preference:
            favorite_mode = max(mode_preference.items(), key=lambda x: x[1])[0]
            logger.info(f"用户常用出行方式: {favorite_mode}")
        else:
            logger.info("未找到用户出行方式偏好")

        # 4. 目的地类型偏好 (通过目的地名称猜测)
        destination_keywords = {
            '购物': ['商场', '超市', '购物中心', '商城', '市场'],
            '餐饮': ['餐厅', '饭店', '美食', '小吃'],
            '休闲': ['公园', '广场', '影院', '电影院', '剧院', '健身'],
            '教育': ['学校', '大学', '学院', '培训', '图书馆'],
            '工作': ['公司', '办公', '写字楼', '工厂']
        }

        category_counts = {category: 0 for category in destination_keywords}
        for route in user_routes:
            dest_name = route.destination_name or ""
            for category, keywords in destination_keywords.items():
                if any(keyword in dest_name for keyword in keywords):
                    category_counts[category] += 1

        # 找出最常访问的目的地类型
        favorite_category = None
        if any(count > 0 for count in category_counts.values()):
            favorite_category = max(category_counts.items(), key=lambda x: x[1])[0]
            logger.info(f"用户偏好目的地类型: {favorite_category}")
        else:
            logger.info("未找到用户目的地类型偏好")

        # 5. 常用起点
        origin_counts = {}
        for route in user_routes:
            origin = route.origin
            if origin not in origin_counts:
                origin_counts[origin] = 0
            origin_counts[origin] += 1

        # 找出最常用的起点
        favorite_origins = sorted(origin_counts.items(), key=lambda x: x[1], reverse=True)[:2]
        favorite_origins = [origin for origin, _ in favorite_origins]
        logger.info(f"用户常用起点数量: {len(favorite_origins)}")

        # 基于分析结果生成推荐
        recommendations = []

        # 1. 推荐相似时间段的路线 (非用户自己的记录)
        if favorite_hours:
            logger.info("开始基于时间段偏好生成推荐")
            # 使用兼容所有数据库的方法，不使用distinct('field')
            seen_routes = set()
            time_based_routes = []

            for route in RouteSearch.objects.filter(
                created_time__hour__in=favorite_hours,
                user__isnull=False
            ).exclude(user=request.user).order_by('-created_time'):
                route_key = f"{route.origin}_{route.destination}"
                if route_key not in seen_routes:
                    seen_routes.add(route_key)
                    time_based_routes.append(route)
                    if len(time_based_routes) >= 3:
                        break

            logger.info(f"找到 {len(time_based_routes)} 条基于时间段的推荐路线")
            for route in time_based_routes:
                recommendations.append({
                    'type': '时间偏好推荐',
                    'origin': route.origin,
                    'destination': route.destination,
                    'origin_name': route.origin_name,
                    'destination_name': route.destination_name,
                    'reason': f'基于您的常用时间段 {", ".join([f"{h}:00" for h in favorite_hours])}',
                    'confidence': 'medium'
                })

        # 2. 推荐相似距离的路线 (非用户自己的记录)
        if avg_distance > 0:
            logger.info("开始基于距离偏好生成推荐")
            # 使用兼容所有数据库的方法，不使用distinct('field')
            seen_routes = set()
            distance_based_routes = []

            for route in RouteSearch.objects.filter(
                distance__gte=distance_lower,
                distance__lte=distance_upper,
                user__isnull=False
            ).exclude(user=request.user).order_by('-created_time'):
                route_key = f"{route.origin}_{route.destination}"
                if route_key not in seen_routes:
                    seen_routes.add(route_key)
                    distance_based_routes.append(route)
                    if len(distance_based_routes) >= 3:
                        break

            logger.info(f"找到 {len(distance_based_routes)} 条基于距离的推荐路线")
            for route in distance_based_routes:
                recommendations.append({
                    'type': '距离偏好推荐',
                    'origin': route.origin,
                    'destination': route.destination,
                    'origin_name': route.origin_name,
                    'destination_name': route.destination_name,
                    'reason': f'基于您的平均出行距离 {round(avg_distance/1000, 1)}公里',
                    'confidence': 'high'
                })

        # 3. 推荐基于常用起点的新目的地
        if favorite_origins:
            logger.info("开始基于常用起点生成推荐")
            for origin in favorite_origins:
                # 使用兼容所有数据库的方法，不使用distinct('field')
                seen_destinations = set()
                similar_routes = []

                # 首先获取用户已经去过的目的地
                user_destinations = set(r.destination for r in user_routes)

                for route in RouteSearch.objects.filter(
                    origin=origin,
                    user__isnull=False
                ).exclude(user=request.user).order_by('-created_time'):
                    if route.destination not in user_destinations and route.destination not in seen_destinations:
                        seen_destinations.add(route.destination)
                        similar_routes.append(route)
                        if len(similar_routes) >= 2:
                            break

                logger.info(f"找到 {len(similar_routes)} 条基于起点 {origin} 的推荐路线")
                for route in similar_routes:
                    recommendations.append({
                        'type': '起点相似推荐',
                        'origin': route.origin,
                        'destination': route.destination,
                        'origin_name': route.origin_name,
                        'destination_name': route.destination_name,
                        'reason': '基于您的常用起点，推荐新的目的地',
                        'confidence': 'high'
                    })

        # 4. 推荐基于目的地类型的新路线
        if favorite_category:
            logger.info(f"开始基于目的地类型 {favorite_category} 生成推荐")
            keywords = destination_keywords.get(favorite_category, [])
            similar_category_routes = []

            # 查找包含相似关键词的目的地
            for route in RouteSearch.objects.filter(user__isnull=False).exclude(user=request.user):
                dest_name = route.destination_name or ""
                if any(keyword in dest_name for keyword in keywords):
                    similar_category_routes.append(route)
                    if len(similar_category_routes) >= 3:
                        break

            logger.info(f"找到 {len(similar_category_routes)} 条基于目的地类型的推荐路线")
            for route in similar_category_routes:
                recommendations.append({
                    'type': '兴趣点推荐',
                    'origin': route.origin,
                    'destination': route.destination,
                    'origin_name': route.origin_name,
                    'destination_name': route.destination_name,
                    'reason': f'基于您对{favorite_category}场所的偏好',
                    'confidence': 'medium'
                })

        # 5. 如果推荐数量不足，添加一些热门路线
        if len(recommendations) < 5:
            needed = 5 - len(recommendations)
            logger.info(f"推荐数量不足，添加 {needed} 条热门路线")
            popular_routes = RouteSearch.objects.values('origin', 'destination', 'origin_name', 'destination_name') \
                .annotate(route_count=Count('id')) \
                .order_by('-route_count')[:needed]

            for route in popular_routes:
                recommendations.append({
                    'type': '热门路线',
                    'origin': route['origin'],
                    'destination': route['destination'],
                    'origin_name': route['origin_name'],
                    'destination_name': route['destination_name'],
                    'reason': '基于系统热门路线推荐',
                    'confidence': 'medium'
                })

        # 移除重复推荐
        unique_recommendations = []
        seen_routes = set()

        for rec in recommendations:
            # 排除起点和终点相同的路线（通过坐标和名称双重检查）
            if rec['origin'] == rec['destination'] or rec['origin_name'] == rec['destination_name']:
                logger.debug(f"排除起终点相同的路线: {rec['origin_name']} → {rec['destination_name']}")
                continue

            route_key = f"{rec['origin']}_{rec['destination']}"
            if route_key not in seen_routes:
                seen_routes.add(route_key)
                unique_recommendations.append(rec)
            else:
                logger.debug(f"排除重复路线: {route_key}")

        logger.info(f"返回 {len(unique_recommendations)} 条基于内容的推荐结果")
        return JsonResponse({
            'status': 'success',
            'data': unique_recommendations,
            'recommendation_type': 'content_based'
        })

    except Exception as e:
        logger.error(f"推荐系统出错: {str(e)}")
        traceback.print_exc()
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        })