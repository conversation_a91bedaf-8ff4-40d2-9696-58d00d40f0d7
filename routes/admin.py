from django.contrib import admin
from django.utils.html import format_html
from django.db.models import Count
from django.urls import reverse
from django.utils.safestring import mark_safe
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from .models import UserProfile, RouteSearch, UserTag

# 自定义 Django 后台标题和头部
admin.site.site_header = "路线规划数据分析系统后台"
admin.site.site_title = "路线系统管理"
admin.site.index_title = "欢迎使用路线规划数据分析系统后台"


class UserTagInline(admin.TabularInline):
    """用户标签内联编辑"""
    model = UserTag
    extra = 0
    verbose_name = "用户标签"
    verbose_name_plural = "用户标签"

class UserProfileInline(admin.StackedInline):
    """用户资料内联编辑"""
    model = UserProfile
    can_delete = False
    verbose_name = "用户资料"
    verbose_name_plural = "用户资料"

# 扩展User管理界面
class UserAdmin(BaseUserAdmin):
    inlines = (UserProfileInline, UserTagInline)

# 重新注册User
admin.site.unregister(User)
admin.site.register(User, UserAdmin)

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'username', 'email', 'phone', 'default_city', 'created_time', 'route_count')
    search_fields = ('user__username', 'user__email', 'phone', 'default_city')
    list_filter = ('default_city', 'created_time')
    readonly_fields = ('created_time', 'route_count')
    fieldsets = (
        ('用户信息', {
            'fields': ('user', 'phone', 'default_city')
        }),
        ('其他信息', {
            'fields': ('common_addresses', 'created_time', 'route_count')
        }),
    )
    
    def username(self, obj):
        """获取用户名"""
        return obj.user.username
    username.short_description = "用户名"
    
    def email(self, obj):
        """获取用户邮箱"""
        return obj.user.email
    email.short_description = "邮箱"
    
    def route_count(self, obj):
        """获取用户的路线数量"""
        count = RouteSearch.objects.filter(user=obj.user).count()
        url = reverse('admin:routes_routesearch_changelist') + f'?user__id__exact={obj.user.id}'
        return format_html('<a href="{}">{} 条路线</a>', url, count)
    route_count.short_description = "路线数量"

@admin.register(RouteSearch)
class RouteSearchAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_link', 'route_display', 'city', 'district', 'travel_purpose', 'distance_display', 'duration_display', 'created_time', 'is_favorite', 'route_tag')
    search_fields = ('origin_name', 'destination_name', 'city', 'district', 'travel_purpose', 'route_tag', 'user__username')
    list_filter = ('city', 'district', 'travel_purpose', 'is_favorite', 'route_tag', 'created_time')
    readonly_fields = ('created_time', 'route_data_display')
    list_per_page = 20
    date_hierarchy = 'created_time'
    actions = ['mark_as_favorite', 'remove_from_favorite']
    fieldsets = (
        ('用户信息', {
            'fields': ('user',)
        }),
        ('路线信息', {
            'fields': ('origin', 'origin_name', 'destination', 'destination_name', 'distance', 'duration')
        }),
        ('分析信息', {
            'fields': ('city', 'district', 'travel_purpose')
        }),
        ('其他信息', {
            'fields': ('is_favorite', 'route_tag', 'created_time')
        }),
        ('详细数据', {
            'fields': ('route_data_display',),
            'classes': ('collapse',)
        }),
    )
    
    def user_link(self, obj):
        """用户链接"""
        if obj.user:
            url = reverse('admin:auth_user_change', args=[obj.user.id])
            return format_html('<a href="{}">{}</a>', url, obj.user.username)
        return "匿名用户"
    user_link.short_description = "用户"
    user_link.admin_order_field = 'user__username'
    
    def route_display(self, obj):
        """路线显示"""
        origin = obj.origin_name or obj.origin
        destination = obj.destination_name or obj.destination
        return format_html('{} → {}', origin, destination)
    route_display.short_description = "路线"
    
    def distance_display(self, obj):
        """距离显示"""
        if obj.distance:
            if obj.distance >= 1000:
                return f"{obj.distance/1000:.1f} 公里"
            return f"{obj.distance} 米"
        return "-"
    distance_display.short_description = "距离"
    distance_display.admin_order_field = 'distance'
    
    def duration_display(self, obj):
        """时间显示"""
        if obj.duration:
            hours = obj.duration // 3600
            minutes = (obj.duration % 3600) // 60
            seconds = obj.duration % 60
            
            if hours > 0:
                return f"{hours}小时{minutes}分钟"
            elif minutes > 0:
                return f"{minutes}分钟{seconds}秒"
            else:
                return f"{seconds}秒"
        return "-"
    duration_display.short_description = "时间"
    duration_display.admin_order_field = 'duration'
    
    def route_data_display(self, obj):
        """路线数据显示"""
        if hasattr(obj, '_route_data') and obj._route_data:
            return mark_safe(f'<pre>{obj._route_data}</pre>')
        return "无路线数据"
    route_data_display.short_description = "路线数据"
    
    def mark_as_favorite(self, request, queryset):
        """标记为收藏"""
        updated = queryset.update(is_favorite=True)
        self.message_user(request, f"成功将 {updated} 条路线标记为收藏")
    mark_as_favorite.short_description = "标记为收藏"
    
    def remove_from_favorite(self, request, queryset):
        """取消收藏"""
        updated = queryset.update(is_favorite=False)
        self.message_user(request, f"成功将 {updated} 条路线取消收藏")
    remove_from_favorite.short_description = "取消收藏"



@admin.register(UserTag)
class UserTagAdmin(admin.ModelAdmin):
    list_display = ('id', 'user_link', 'name', 'created_time', 'route_count')
    search_fields = ('user__username', 'name')
    list_filter = ('created_time',)
    readonly_fields = ('created_time', 'route_count')
    
    def user_link(self, obj):
        """用户链接"""
        url = reverse('admin:auth_user_change', args=[obj.user.id])
        return format_html('<a href="{}">{}</a>', url, obj.user.username)
    user_link.short_description = "用户"
    user_link.admin_order_field = 'user__username'
    
    def route_count(self, obj):
        """获取使用该标签的路线数量"""
        count = RouteSearch.objects.filter(route_tag=obj.name, user=obj.user).count()
        if count > 0:
            url = reverse('admin:routes_routesearch_changelist') + f'?route_tag__exact={obj.name}&user__id__exact={obj.user.id}'
            return format_html('<a href="{}">{} 条路线</a>', url, count)
        return "0 条路线"
    route_count.short_description = "使用该标签的路线数量"
