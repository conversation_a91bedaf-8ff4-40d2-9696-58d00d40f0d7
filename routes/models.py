from django.db import models
from django.contrib.auth.models import User
import json

# Create your models here.

class UserProfile(models.Model):
    """
    用户个人资料扩展
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    phone = models.CharField(max_length=20, blank=True, null=True, verbose_name='手机号')
    default_city = models.CharField(max_length=50, blank=True, null=True, verbose_name='默认城市')
    common_addresses = models.TextField(blank=True, null=True, verbose_name='常用地址')
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    def get_common_addresses(self):
        """
        获取常用地址列表
        """
        if not self.common_addresses:
            return []
        try:
            return json.loads(self.common_addresses)
        except:
            return []
    
    def add_common_address(self, name, coordinates):
        """
        添加常用地址
        """
        addresses = self.get_common_addresses()
        # 检查是否已存在
        for addr in addresses:
            if addr.get('coordinates') == coordinates:
                return False
        
        # 添加新地址
        addresses.append({
            'name': name,
            'coordinates': coordinates
        })
        
        # 如果超过10个地址 删除最早的
        if len(addresses) > 10:
            addresses.pop(0)
        
        self.common_addresses = json.dumps(addresses)
        self.save()
        return True
    
    class Meta:
        verbose_name = '用户资料'
        verbose_name_plural = '用户资料'
        
    def __str__(self):
        return f"{self.user.username}的个人资料"

class RouteSearch(models.Model):
    """
    路线搜索记录
    包含基础路线信息和数据分析字段
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True, related_name='route_searches')
    origin = models.CharField(max_length=255, verbose_name='起点坐标')
    destination = models.CharField(max_length=255, verbose_name='终点坐标')
    origin_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='起点名称')
    destination_name = models.CharField(max_length=255, blank=True, null=True, verbose_name='终点名称')
    distance = models.IntegerField(null=True, blank=True, verbose_name='距离(米)')
    duration = models.IntegerField(null=True, blank=True, verbose_name='时间(秒)')
    _route_data = models.TextField(db_column='route_data', blank=True, null=True, verbose_name='路线数据JSON')
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间', db_index=True)
    is_favorite = models.BooleanField(default=False, verbose_name='是否收藏')
    route_tag = models.CharField(max_length=50, blank=True, default='', verbose_name='路线标签')

    # 数据分析相关字段
    city = models.CharField(max_length=50, blank=True, null=True, verbose_name='城市')
    district = models.CharField(max_length=50, blank=True, null=True, verbose_name='区县')
    travel_purpose = models.CharField(max_length=50, blank=True, null=True, verbose_name='出行目的')
    
    @property
    def route_data(self):
        """获取解析后的路线数据"""
        if not self._route_data:
            return {}
        
        try:
            return json.loads(self._route_data)
        except Exception as e:
            print(f"解析路线数据出错: {e}")
            return {}
    
    @route_data.setter
    def route_data(self, value):
        """设置路线数据"""
        if isinstance(value, dict):
            self._route_data = json.dumps(value)
        elif isinstance(value, str):
            # 尝试解析验证是否为有效的JSON
            try:
                json.loads(value)
                self._route_data = value
            except Exception as e:
                print(f"保存路线数据出错 无效的JSON: {e}")
        else:
            print(f"无法设置路线数据 类型错误: {type(value)}")
    
    class Meta:
        verbose_name = '路线搜索'
        verbose_name_plural = '路线搜索'
        ordering = ['-created_time']  # 默认按创建时间倒序排列
        
    def __str__(self):
        return f"{self.origin_name or self.origin}   {self.destination_name or self.destination}"



class UserTag(models.Model):
    """
    用户标签模型
    用于存储用户自定义的路线标签
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='tags', verbose_name='用户')
    name = models.CharField(max_length=50, verbose_name='标签名称')
    created_time = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    
    class Meta:
        verbose_name = '用户标签'
        verbose_name_plural = '用户标签'
        unique_together = ('user', 'name')  # 同一用户不能有重复的标签名
        ordering = ['name']
    
    def __str__(self):
        return f"{self.user.username} - {self.name}"
