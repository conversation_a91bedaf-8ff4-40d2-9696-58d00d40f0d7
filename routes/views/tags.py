"""
标签管理相关视图
"""
import json
from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from datetime import timedelta

from ..models import UserTag, RouteSearch

def tags_view(request):
    """
    标签管理页面视图
    未登录用户将看到登录提示
    """
    # 检查用户是否已登录
    if not request.user.is_authenticated:
        # 未登录用户显示登录提示
        return render(request, 'routes/tags.html', {
            'not_authenticated': True
        })
    
    # 已登录用户正常显示标签管理页面
    return render(request, 'routes/tags.html')

@login_required
def get_user_tags(request):
    """获取用户标签列表API"""
    try:
        # 获取用户所有标签
        tags = UserTag.objects.filter(user=request.user)
        
        # 统计每个标签的使用次数
        tag_counts = {}
        routes = RouteSearch.objects.filter(user=request.user)
        
        for route in routes:
            tag = route.route_tag.strip() if route.route_tag else ''
            if tag:
                if tag not in tag_counts:
                    tag_counts[tag] = 0
                tag_counts[tag] += 1
        
        # 格式化标签数据
        tags_data = []
        for tag in tags:
            tags_data.append({
                'id': tag.id,
                'name': tag.name,
                'count': tag_counts.get(tag.name, 0)
            })
        
        # 按使用次数排序
        tags_data.sort(key=lambda x: x['count'], reverse=True)
        
        return JsonResponse({
            'status': 'success',
            'tags': tags_data
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'获取标签失败: {str(e)}'
        })

@login_required
@csrf_exempt
def save_user_tag(request):
    """保存用户标签API"""
    if request.method == 'POST':
        try:
            # 解析请求数据
            data = json.loads(request.body)
            tag_name = data.get('name', '').strip()
            
            if not tag_name:
                return JsonResponse({
                    'status': 'error',
                    'message': '标签名称不能为空'
                })
            
            # 检查标签是否已存在
            if UserTag.objects.filter(user=request.user, name=tag_name).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': '该标签已存在'
                })
            
            # 创建新标签
            tag = UserTag.objects.create(
                user=request.user,
                name=tag_name
            )
            
            return JsonResponse({
                'status': 'success',
                'tag': {
                    'id': tag.id,
                    'name': tag.name
                }
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'保存标签失败: {str(e)}'
            })
    
    return JsonResponse({
        'status': 'error',
        'message': '仅支持POST请求'
    })

@login_required
@csrf_exempt
def delete_user_tag(request, tag_id):
    """删除用户标签API"""
    if request.method == 'DELETE':
        try:
            # 查找标签
            tag = UserTag.objects.get(id=tag_id, user=request.user)
            tag_name = tag.name
            
            # 删除标签
            tag.delete()
            
            # 清除相关路线的标签
            RouteSearch.objects.filter(user=request.user, route_tag=tag_name).update(route_tag='')
            
            return JsonResponse({
                'status': 'success',
                'message': '标签已删除'
            })
        except UserTag.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': '标签不存在或无权操作'
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'删除标签失败: {str(e)}'
            })
    
    return JsonResponse({
        'status': 'error',
        'message': '仅支持DELETE请求'
    })

@login_required
def get_tag_stats(request):
    """获取标签统计数据API"""
    try:
        # 获取用户所有路线
        routes = RouteSearch.objects.filter(user=request.user)
        
        # 统计标签使用情况
        tag_stats = {}
        for route in routes:
            tag = route.route_tag.strip() if route.route_tag else '未标记'
            if not tag:
                tag = '未标记'
            
            if tag not in tag_stats:
                tag_stats[tag] = 0
            
            tag_stats[tag] += 1
        
        # 格式化为图表所需数据
        stats_data = []
        for tag, count in tag_stats.items():
            stats_data.append({
                'name': tag,
                'count': count
            })
        
        # 按使用次数排序
        stats_data.sort(key=lambda x: x['count'], reverse=True)
        
        return JsonResponse({
            'status': 'success',
            'stats': stats_data
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'获取标签统计失败: {str(e)}'
        })

@login_required
@csrf_exempt
def apply_batch_tag(request):
    """批量应用标签API"""
    if request.method == 'POST':
        try:
            # 解析请求数据
            data = json.loads(request.body)
            tag_id = data.get('tag_id')
            apply_to_recent = data.get('apply_to_recent', False)
            apply_to_untagged = data.get('apply_to_untagged', False)
            apply_to_favorites = data.get('apply_to_favorites', False)
            
            # 获取标签
            tag = UserTag.objects.get(id=tag_id, user=request.user)
            
            # 构建基础查询条件
            from django.db.models import Q

            # 基础条件：只查询当前用户的记录
            query = Q(user=request.user)

            if apply_to_recent:
                # 最近7天的记录
                seven_days_ago = timezone.now() - timedelta(days=7)
                query &= Q(created_time__gte=seven_days_ago)

            if apply_to_untagged:
                # 未标记的记录（包括NULL、空字符串、只有空格的字符串）
                untagged_query = Q(route_tag__isnull=True) | Q(route_tag='') | Q(route_tag__regex=r'^\s*$')
                query &= untagged_query

            if apply_to_favorites:
                # 收藏的记录
                query &= Q(is_favorite=True)

            # 查询符合条件的记录
            routes = RouteSearch.objects.filter(query)
            
            # 应用标签
            updated_count = routes.update(route_tag=tag.name)
            
            return JsonResponse({
                'status': 'success',
                'message': '批量应用标签成功',
                'updated_count': updated_count
            })
        except UserTag.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': '标签不存在或无权操作'
            })
        except Exception as e:
            return JsonResponse({
                'status': 'error',
                'message': f'批量应用标签失败: {str(e)}'
            })
    
    return JsonResponse({
        'status': 'error',
        'message': '仅支持POST请求'
    }) 