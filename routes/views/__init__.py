"""
视图模块初始化文件
导入所有视图函数，使它们可以被外部访问
"""
# 导入核心视图
from .core import (
    index,
    route_planning,
    route_history,
    route_detail,
    route_history_all,
    toggle_favorite,
    update_route_tag,
    analytics_view,
    search_view,
    recommendations_view
)

# 导入用户认证相关视图
from .auth import (
    login_view,
    register_view,
    logout_view
)

# 导入用户资料和地址管理相关视图
from .profile import (
    profile_view,
    add_favorite_address,
    delete_favorite_address
)

# 导入搜索相关视图
from .search import (
    search_routes,
    search_routes_v2
)

# 导入标签管理相关视图
from .tags import (
    tags_view,
    get_user_tags,
    save_user_tag,
    delete_user_tag,
    get_tag_stats,
    apply_batch_tag
)

# 导入分析相关视图
from .analytics import (
    analytics_overview,
    analytics_mode_distribution,
    analytics_distance_distribution,
    analytics_popular_destinations,
    analytics_daily_trend,
    analytics_hourly_distribution,
    analytics_monthly_trend,
    analytics_heatmap_data,
    analytics_route_density_data,
    analytics_popular_routes,
    analytics_mode_comparison,
    analytics_distance_time_relation,
    analytics_tag_analysis,
    analytics_user_comparison
)



