"""
生成模拟数据的Django管理命令
"""
import random
import json
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from routes.models import UserProfile, RouteSearch, UserTag


class Command(BaseCommand):
    """
    生成模拟数据的命令
    """
    help = '生成模拟数据用于测试和演示'

    def add_arguments(self, parser):
        """添加命令行参数"""
        parser.add_argument('--users', type=int, default=20, help='要生成的用户数量')
        parser.add_argument('--routes', type=int, default=100, help='每个用户的路线搜索记录数量')
        parser.add_argument('--admin', action='store_true', help='是否创建管理员用户')

    def handle(self, *args, **options):
        """命令处理函数"""
        user_count = options['users']
        routes_per_user = options['routes']
        create_admin = options['admin']

        self.stdout.write(self.style.SUCCESS(f'开始生成模拟数据: {user_count}个用户, 每个用户{routes_per_user}条路线记录'))

        # 城市和区县数据
        cities = ['北京', '上海', '广州', '深圳', '杭州', '南京', '成都', '武汉', '西安', '重庆']
        districts = {
            '北京': ['海淀区', '朝阳区', '东城区', '西城区', '丰台区', '石景山区', '昌平区', '顺义区'],
            '上海': ['浦东新区', '黄浦区', '静安区', '徐汇区', '长宁区', '虹口区', '杨浦区', '闵行区'],
            '广州': ['天河区', '越秀区', '海珠区', '荔湾区', '白云区', '黄埔区', '番禺区', '花都区'],
            '深圳': ['福田区', '罗湖区', '南山区', '宝安区', '龙岗区', '盐田区', '龙华区', '坪山区'],
            '杭州': ['西湖区', '上城区', '下城区', '江干区', '拱墅区', '滨江区', '余杭区', '萧山区'],
            '南京': ['玄武区', '秦淮区', '建邺区', '鼓楼区', '浦口区', '栖霞区', '雨花台区', '江宁区'],
            '成都': ['锦江区', '青羊区', '金牛区', '武侯区', '成华区', '龙泉驿区', '新都区', '温江区'],
            '武汉': ['江岸区', '江汉区', '硚口区', '汉阳区', '武昌区', '青山区', '洪山区', '东西湖区'],
            '西安': ['新城区', '碑林区', '莲湖区', '灞桥区', '未央区', '雁塔区', '阎良区', '临潼区'],
            '重庆': ['渝中区', '大渡口区', '江北区', '沙坪坝区', '九龙坡区', '南岸区', '北碚区', '渝北区']
        }

        # 出行目的
        travel_purposes = ['上班', '购物', '旅游', '探亲', '就医', '上学', '商务', '娱乐', '健身', '其他']

        # 路线标签
        tags = ['工作', '生活', '学习', '娱乐', '家庭', '朋友', '重要', '紧急', '常用', '临时', '周末', '假日']

        # 交通方式
        travel_modes = ['driving', 'walking', 'transit', 'cycling']

        # 模拟地址数据 (经纬度)
        locations = {
            '北京': [
                {'name': '北京站', 'coordinates': '116.427287,39.904179'},
                {'name': '北京西站', 'coordinates': '116.321592,39.894742'},
                {'name': '北京南站', 'coordinates': '116.378517,39.865246'},
                {'name': '天安门', 'coordinates': '116.397428,39.908649'},
                {'name': '故宫', 'coordinates': '116.403882,39.917219'},
                {'name': '颐和园', 'coordinates': '116.273921,40.000831'},
                {'name': '清华大学', 'coordinates': '116.333374,40.009645'},
                {'name': '北京大学', 'coordinates': '116.310905,39.992806'},
                {'name': '中关村', 'coordinates': '116.310316,39.985568'},
                {'name': '望京SOHO', 'coordinates': '116.482861,39.996434'},
                {'name': '三里屯', 'coordinates': '116.454631,39.938265'},
                {'name': '798艺术区', 'coordinates': '116.495028,39.984078'},
                {'name': '国家体育场', 'coordinates': '116.396903,39.992708'},
                {'name': '首都国际机场', 'coordinates': '116.603111,40.079613'},
                {'name': '北京动物园', 'coordinates': '116.341455,39.947614'}
            ],
            '上海': [
                {'name': '上海站', 'coordinates': '121.456184,31.249702'},
                {'name': '上海虹桥站', 'coordinates': '121.32299,31.193317'},
                {'name': '上海南站', 'coordinates': '121.429533,31.154861'},
                {'name': '外滩', 'coordinates': '121.490317,31.236658'},
                {'name': '陆家嘴', 'coordinates': '121.506377,31.241701'},
                {'name': '上海迪士尼', 'coordinates': '121.674272,31.146027'},
                {'name': '复旦大学', 'coordinates': '121.500825,31.300898'},
                {'name': '上海交通大学', 'coordinates': '121.435762,31.203106'},
                {'name': '静安寺', 'coordinates': '121.445734,31.223695'},
                {'name': '上海科技馆', 'coordinates': '121.555725,31.182848'},
                {'name': '田子坊', 'coordinates': '121.470921,31.210516'},
                {'name': '豫园', 'coordinates': '121.492153,31.227203'},
                {'name': '上海博物馆', 'coordinates': '121.475822,31.22775'},
                {'name': '浦东国际机场', 'coordinates': '121.808273,31.155195'},
                {'name': '上海野生动物园', 'coordinates': '121.728727,31.059897'}
            ],
            '广州': [
                {'name': '广州站', 'coordinates': '113.258543,23.148131'},
                {'name': '广州南站', 'coordinates': '113.269505,22.988967'},
                {'name': '广州东站', 'coordinates': '113.324851,23.150968'},
                {'name': '天河城', 'coordinates': '113.327869,23.132345'},
                {'name': '珠江新城', 'coordinates': '113.322462,23.119356'},
                {'name': '白云山', 'coordinates': '113.311148,23.183821'},
                {'name': '中山大学', 'coordinates': '113.297369,23.095605'},
                {'name': '华南理工大学', 'coordinates': '113.348394,23.153035'},
                {'name': '北京路步行街', 'coordinates': '113.275052,23.124861'},
                {'name': '广州塔', 'coordinates': '113.330948,23.113586'},
                {'name': '上下九步行街', 'coordinates': '113.280675,23.11431'},
                {'name': '沙面', 'coordinates': '113.245712,23.109292'},
                {'name': '广州博物馆', 'coordinates': '113.256135,23.144766'},
                {'name': '白云国际机场', 'coordinates': '113.307728,23.396726'},
                {'name': '长隆野生动物世界', 'coordinates': '113.32315,22.998341'}
            ],
            '深圳': [
                {'name': '深圳站', 'coordinates': '114.117317,22.531933'},
                {'name': '深圳北站', 'coordinates': '114.029533,22.609062'},
                {'name': '深圳湾口岸', 'coordinates': '113.946334,22.495844'},
                {'name': '华强北', 'coordinates': '114.090046,22.548457'},
                {'name': '福田中心区', 'coordinates': '114.055853,22.543478'},
                {'name': '世界之窗', 'coordinates': '113.979933,22.535296'},
                {'name': '深圳大学', 'coordinates': '113.937996,22.532998'},
                {'name': '南方科技大学', 'coordinates': '114.000364,22.603256'},
                {'name': '东门步行街', 'coordinates': '114.118326,22.545229'},
                {'name': '莲花山公园', 'coordinates': '114.065933,22.558701'},
                {'name': '海上世界', 'coordinates': '113.922614,22.479734'},
                {'name': '大梅沙', 'coordinates': '114.317558,22.598219'},
                {'name': '深圳博物馆', 'coordinates': '114.065853,22.541748'},
                {'name': '宝安国际机场', 'coordinates': '113.814321,22.631244'},
                {'name': '欢乐谷', 'coordinates': '114.069517,22.546251'}
            ]
        }

        # 为其他城市生成随机坐标
        for city in cities:
            if city not in locations:
                locations[city] = []
                for i in range(15):
                    # 随机生成经纬度
                    if city in ['北京', '上海', '广州', '深圳']:
                        continue
                    lng = random.uniform(102.0, 122.0)
                    lat = random.uniform(22.0, 40.0)
                    locations[city].append({
                        'name': f'{city}地点{i+1}',
                        'coordinates': f'{lng:.6f},{lat:.6f}'
                    })

        try:
            with transaction.atomic():
                # 创建管理员用户
                if create_admin:
                    admin_user, created = User.objects.get_or_create(
                        username='admin',
                        defaults={
                            'email': '<EMAIL>',
                            'is_staff': True,
                            'is_superuser': True
                        }
                    )
                    if created:
                        admin_user.set_password('admin123')
                        admin_user.save()
                        UserProfile.objects.create(
                            user=admin_user,
                            phone='13800000000',
                            default_city='北京'
                        )
                        self.stdout.write(self.style.SUCCESS('已创建管理员用户: admin (密码: admin123)'))

                # 创建普通用户
                for i in range(1, user_count + 1):
                    username = f'user{i}'
                    
                    # 检查用户是否已存在
                    if User.objects.filter(username=username).exists():
                        user = User.objects.get(username=username)
                        self.stdout.write(self.style.WARNING(f'用户 {username} 已存在，跳过创建'))
                    else:
                        # 创建新用户
                        user = User.objects.create_user(
                            username=username,
                            email=f'user{i}@example.com',
                            password=f'password{i}',
                            first_name=f'用户',
                            last_name=f'{i}号'
                        )
                        
                        # 随机选择一个城市作为默认城市
                        city = random.choice(cities)
                        
                        # 创建用户资料
                        profile = UserProfile.objects.create(
                            user=user,
                            phone=f'138{i:08d}',
                            default_city=city
                        )
                        
                        # 添加几个常用地址
                        city_locations = locations.get(city, [])
                        if city_locations:
                            common_addresses = []
                            for _ in range(min(5, len(city_locations))):
                                loc = random.choice(city_locations)
                                common_addresses.append({
                                    'name': loc['name'],
                                    'coordinates': loc['coordinates']
                                })
                            profile.common_addresses = json.dumps(common_addresses)
                            profile.save()
                        
                        # 创建用户标签
                        user_tags = random.sample(tags, min(5, len(tags)))
                        for tag in user_tags:
                            UserTag.objects.create(user=user, name=tag)
                        
                        self.stdout.write(f'已创建用户: {username} (密码: password{i})')
                    
                    # 为用户创建路线搜索记录
                    self._create_routes_for_user(user, routes_per_user, cities, districts, travel_purposes, travel_modes, locations)
                
                self.stdout.write(self.style.SUCCESS('模拟数据生成完成!'))
        
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'生成数据时出错: {str(e)}'))
    
    def _create_routes_for_user(self, user, count, cities, districts, travel_purposes, travel_modes, locations):
        """为用户创建路线搜索记录"""
        # 获取用户的默认城市
        try:
            profile = UserProfile.objects.get(user=user)
            default_city = profile.default_city or random.choice(cities)
        except UserProfile.DoesNotExist:
            default_city = random.choice(cities)
        
        # 获取用户的标签
        user_tags = list(UserTag.objects.filter(user=user).values_list('name', flat=True))
        if not user_tags:
            user_tags = ['工作', '生活', '学习']  # 默认标签
        
        # 生成路线记录
        for _ in range(count):
            # 80%的概率使用默认城市，20%的概率使用其他城市
            if random.random() < 0.8:
                city = default_city
            else:
                city = random.choice([c for c in cities if c != default_city])
            
            # 选择区县
            district = random.choice(districts.get(city, ['未知区']))
            
            # 选择出行目的
            purpose = random.choice(travel_purposes)
            
            # 选择交通方式
            mode = random.choice(travel_modes)
            
            # 选择起点和终点
            city_locations = locations.get(city, [])
            if not city_locations:
                # 如果没有该城市的位置数据，跳过
                continue
            
            origin = random.choice(city_locations)
            destination = random.choice([loc for loc in city_locations if loc != origin])
            
            # 计算随机的距离和时间
            if mode == 'driving':
                distance = random.randint(2000, 30000)  # 2-30公里
                duration = distance // 10 * (random.uniform(0.8, 1.2))  # 平均速度约36km/h
            elif mode == 'walking':
                distance = random.randint(500, 5000)  # 500米-5公里
                duration = distance // 1.2 * (random.uniform(0.9, 1.1))  # 平均速度约4.3km/h
            elif mode == 'transit':
                distance = random.randint(1000, 20000)  # 1-20公里
                duration = distance // 8 * (random.uniform(0.7, 1.3))  # 平均速度约28.8km/h
            else:  # cycling
                distance = random.randint(1000, 10000)  # 1-10公里
                duration = distance // 3.5 * (random.uniform(0.8, 1.2))  # 平均速度约12.6km/h
            
            # 生成随机的创建时间（过去30天内）
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            created_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
            
            # 是否收藏（10%的概率）
            is_favorite = random.random() < 0.1
            
            # 随机选择标签（30%的概率有标签）
            route_tag = random.choice(user_tags) if random.random() < 0.3 else ''
            
            # 创建路线搜索记录
            route_data = {
                'mode': mode,
                'distance': distance,
                'duration': duration,
                'steps': [
                    {
                        'instruction': f'从{origin["name"]}出发',
                        'distance': distance // 3,
                        'duration': duration // 3
                    },
                    {
                        'instruction': f'继续前行',
                        'distance': distance // 3,
                        'duration': duration // 3
                    },
                    {
                        'instruction': f'到达{destination["name"]}',
                        'distance': distance // 3,
                        'duration': duration // 3
                    }
                ]
            }
            
            route_search = RouteSearch(
                user=user,
                origin=origin['coordinates'],
                destination=destination['coordinates'],
                origin_name=origin['name'],
                destination_name=destination['name'],
                distance=distance,
                duration=duration,
                route_data=route_data,
                created_time=created_time,
                is_favorite=is_favorite,
                route_tag=route_tag
            )
            route_search.save()